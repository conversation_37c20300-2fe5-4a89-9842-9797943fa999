import{A as r}from"./ApplicationLogo-BIrQV_G-.js";import{r as l,p as n,q as a,g as i,o as d,b as t,a as s,w as c,d as u,l as f,s as m,L as p}from"./app-wnQ52fJE.js";const _={class:"flex min-h-screen flex-col items-center bg-gray-100 pt-6 sm:justify-center sm:pt-0"},h={class:"mt-6 w-full overflow-hidden bg-white px-6 py-4 shadow-md sm:max-w-md sm:rounded-lg"},y={__name:"GuestLayout",setup(w){const e=l(!1);return n(()=>{a.on("start",()=>{e.value=!0}),a.on("finish",()=>{e.value=!1})}),(o,g)=>(d(),i("div",_,[t("div",null,[s(u(f),{href:"/"},{default:c(()=>[s(r,{class:"h-20 w-20 fill-current text-gray-500"})]),_:1})]),t("div",h,[m(o.$slots,"default")]),s(p,{show:e.value},null,8,["show"])]))}};export{y as _};
