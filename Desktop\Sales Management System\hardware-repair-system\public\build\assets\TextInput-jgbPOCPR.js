import{j as u,N as d,g as t,o as s,b as i,t as l,s as p,Q as m,r as _,p as f,v as g}from"./app-wnQ52fJE.js";const v={class:"text-sm text-red-600"},S={__name:"InputError",props:{message:{type:String}},setup(e){return(a,o)=>u((s(),t("div",null,[i("p",v,l(e.message),1)],512)),[[d,e.message]])}},h={class:"block text-sm font-medium text-gray-300"},x={key:0},y={key:1},V={__name:"InputLabel",props:{value:{type:String}},setup(e){return(a,o)=>(s(),t("label",h,[e.value?(s(),t("span",x,l(e.value),1)):(s(),t("span",y,[p(a.$slots,"default")]))]))}},w={__name:"TextInput",props:{modelValue:{type:String,required:!0},modelModifiers:{}},emits:["update:modelValue"],setup(e,{expose:a}){const o=m(e,"modelValue"),r=_(null);return f(()=>{r.value.hasAttribute("autofocus")&&r.value.focus()}),a({focus:()=>r.value.focus()}),(b,n)=>u((s(),t("input",{class:"rounded-lg bg-gray-800 border-gray-700 text-white placeholder-gray-400 shadow-sm focus:border-red-500 focus:ring-red-500 transition-colors duration-200","onUpdate:modelValue":n[0]||(n[0]=c=>o.value=c),ref_key:"input",ref:r},null,512)),[[g,o.value]])}};export{V as _,w as a,S as b};
