import{r as c,x as q,q as B,g as n,o as r,a as f,d as y,h as G,w as m,b as e,t as l,i as u,F as w,y as A,n as _,l as C,c as S,f as $,j as D,v as Q,I as F}from"./app-wnQ52fJE.js";import{_ as J}from"./AuthenticatedLayout-D449e8ZD.js";import{_ as K}from"./TechnicianModal-VAFlpPWT.js";import{_ as O}from"./ConfirmationModal-D9qANocE.js";import"./ApplicationLogo-BIrQV_G-.js";import"./DropdownLink-BLptVuux.js";const X={class:"flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0"},Y={class:"flex flex-col sm:flex-row items-start sm:items-center space-y-3 sm:space-y-0 sm:space-x-3"},Z={class:"relative flex-1 max-w-md"},ee={class:"flex items-center space-x-2"},te=["value"],se={class:"flex items-center space-x-2"},oe={class:"p-6 space-y-6"},re={class:"grid grid-cols-1 md:grid-cols-4 gap-6"},ae={class:"bg-gradient-to-br from-gray-900 to-gray-800 border border-gray-700 rounded-xl p-4 shadow-lg"},le={class:"flex items-center justify-between"},ne={class:"text-2xl font-bold text-white"},ie={class:"bg-gradient-to-br from-gray-900 to-gray-800 border border-gray-700 rounded-xl p-4 shadow-lg"},de={class:"flex items-center justify-between"},ce={class:"text-2xl font-bold text-white"},ue={class:"bg-gradient-to-br from-gray-900 to-gray-800 border border-gray-700 rounded-xl p-4 shadow-lg"},ve={class:"flex items-center justify-between"},xe={class:"text-2xl font-bold text-white"},ge={class:"bg-gradient-to-br from-gray-900 to-gray-800 border border-gray-700 rounded-xl p-4 shadow-lg"},he={class:"flex items-center justify-between"},pe={class:"text-2xl font-bold text-white"},fe={class:"bg-gradient-to-br from-gray-900 to-gray-800 border border-gray-700 rounded-xl shadow-xl overflow-hidden"},ye={class:"overflow-x-auto"},me={class:"min-w-full divide-y divide-gray-700"},we={class:"bg-gray-900 divide-y divide-gray-700"},be={class:"px-6 py-4 whitespace-nowrap"},ke={class:"flex items-center"},_e={class:"w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center mr-4 shadow-lg group-hover:shadow-xl transition-shadow duration-200"},Ce={class:"text-sm font-bold text-white"},Me={class:"text-sm font-semibold text-white group-hover:text-red-300 transition-colors duration-200"},je={class:"text-xs text-gray-400"},Te={key:0,class:"text-xs text-gray-400"},ze={class:"px-6 py-4 whitespace-nowrap"},Be={class:"text-sm font-medium text-white"},Ae={class:"px-6 py-4 whitespace-nowrap"},Se={class:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800 shadow-sm"},De={class:"px-6 py-4 whitespace-nowrap"},He={class:"px-6 py-4 whitespace-nowrap"},Ve={class:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium"},Le={class:"flex items-center justify-end space-x-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200"},$e=["onClick"],Fe=["onClick","title"],Ne={key:0,class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},Ee={key:1,class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},Ie={key:0},Pe={colspan:"7",class:"px-6 py-12 text-center"},Re={class:"flex flex-col items-center"},Ue={class:"text-lg font-medium text-white mb-2"},We={class:"text-gray-400 text-sm mb-4"},qe={key:0,class:"bg-gray-800 px-4 py-3 border-t border-gray-700 sm:px-6"},Ge={class:"flex items-center justify-between"},Qe={class:"flex-1 flex justify-between sm:hidden"},Je={class:"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between"},Ke={class:"text-sm text-gray-400"},Oe={class:"relative z-0 inline-flex rounded-md shadow-sm -space-x-px"},Xe=["innerHTML"],rt={__name:"Index",props:{technicians:Object,specializations:Array,filters:Object},setup(a){const g=a,h=c(!1),b=c(!1),M=c(null),d=c(null),p=c(!1),i=c(g.filters?.search||""),v=c(g.filters?.status||"all"),x=c(g.filters?.specialization||"all"),j=c(g.filters?.sort||"created_at"),T=c(g.filters?.direction||"desc");let H;q(i,o=>{clearTimeout(H),H=setTimeout(()=>{k()},300)});const V=()=>{M.value=null,h.value=!0},N=o=>{M.value=o,h.value=!0},E=o=>{d.value=o,b.value=!0},I=()=>{h.value=!1,B.reload({only:["technicians"]})},P=()=>{d.value&&(p.value=!0,B.patch(route("technicians.toggle-status",d.value.id),{},{onSuccess:o=>{window.toast?window.toast.success(o.props?.flash?.success||"Technician status updated successfully!"):alert(o.props?.flash?.success||"Technician status updated successfully!"),b.value=!1,d.value=null,p.value=!1},onError:o=>{window.toast?window.toast.error(o.error||"Failed to update technician status. Please try again."):alert(o.error||"Failed to update technician status. Please try again."),p.value=!1}}))},R=()=>{b.value=!1,d.value=null,p.value=!1},k=()=>{const o={search:i.value||void 0,status:v.value!=="all"?v.value:void 0,specialization:x.value!=="all"?x.value:void 0,sort:j.value!=="created_at"?j.value:void 0,direction:T.value!=="desc"?T.value:void 0};B.get(route("technicians.index"),o,{preserveState:!0,replace:!0})},L=()=>{i.value="",v.value="all",x.value="all",j.value="created_at",T.value="desc",k()},U=o=>o?"bg-green-100 text-green-800 border-green-200":"bg-red-100 text-red-800 border-red-200",W=o=>o===0?"bg-gray-100 text-gray-800 border-gray-200":o<=3?"bg-green-100 text-green-800 border-green-200":o<=6?"bg-yellow-100 text-yellow-800 border-yellow-200":"bg-red-100 text-red-800 border-red-200";return(o,t)=>(r(),n(w,null,[f(y(G),{title:"Technicians"}),f(J,null,{header:m(()=>[e("div",X,[t[10]||(t[10]=e("div",null,[e("h2",{class:"text-2xl font-bold text-white mb-1"}," Technician Management "),e("p",{class:"text-gray-400 text-sm"},"Manage repair technicians and their profiles")],-1)),e("div",Y,[e("div",Z,[D(e("input",{"onUpdate:modelValue":t[0]||(t[0]=s=>i.value=s),type:"text",placeholder:"Search technicians...",class:"w-full bg-gray-800 border border-gray-700 text-white placeholder-gray-400 rounded-lg px-4 py-2 pl-10 focus:ring-2 focus:ring-red-500 focus:border-transparent transition-colors duration-200"},null,512),[[Q,i.value]]),t[6]||(t[6]=e("svg",{class:"absolute left-3 top-2.5 w-4 h-4 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})],-1)),i.value?(r(),n("button",{key:0,onClick:t[1]||(t[1]=s=>i.value=""),class:"absolute right-3 top-2.5 text-gray-400 hover:text-white"},t[5]||(t[5]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))):u("",!0)]),e("div",ee,[D(e("select",{"onUpdate:modelValue":t[2]||(t[2]=s=>v.value=s),onChange:k,class:"bg-gray-800 border border-gray-700 text-white rounded-lg px-4 py-2 focus:ring-2 focus:ring-red-500 focus:border-transparent"},t[7]||(t[7]=[e("option",{value:"all"},"All Status",-1),e("option",{value:"active"},"Active",-1),e("option",{value:"inactive"},"Inactive",-1)]),544),[[F,v.value]]),D(e("select",{"onUpdate:modelValue":t[3]||(t[3]=s=>x.value=s),onChange:k,class:"bg-gray-800 border border-gray-700 text-white rounded-lg px-4 py-2 focus:ring-2 focus:ring-red-500 focus:border-transparent"},[t[8]||(t[8]=e("option",{value:"all"},"All Specializations",-1)),(r(!0),n(w,null,A(a.specializations,s=>(r(),n("option",{key:s,value:s},l(s),9,te))),128))],544),[[F,x.value]])]),e("div",se,[i.value||v.value!=="all"||x.value!=="all"?(r(),n("button",{key:0,onClick:L,class:"bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors duration-200"}," Clear ")):u("",!0),e("button",{onClick:V,class:"bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 text-white px-6 py-2 rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl flex items-center space-x-2"},t[9]||(t[9]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),e("span",null,"Add Technician",-1)]))])])])]),default:m(()=>[e("div",oe,[e("div",re,[e("div",ae,[e("div",le,[e("div",null,[t[11]||(t[11]=e("p",{class:"text-sm text-gray-400"},"Total Technicians",-1)),e("p",ne,l(a.technicians.total||0),1)]),t[12]||(t[12]=e("div",{class:"p-2 bg-blue-600 rounded-lg"},[e("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})])],-1))])]),e("div",ie,[e("div",de,[e("div",null,[t[13]||(t[13]=e("p",{class:"text-sm text-gray-400"},"Active",-1)),e("p",ce,l(a.technicians.data?.filter(s=>s.is_active).length||0),1)]),t[14]||(t[14]=e("div",{class:"p-2 bg-green-600 rounded-lg"},[e("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1))])]),e("div",ue,[e("div",ve,[e("div",null,[t[15]||(t[15]=e("p",{class:"text-sm text-gray-400"},"Specializations",-1)),e("p",xe,l(a.specializations.length),1)]),t[16]||(t[16]=e("div",{class:"p-2 bg-purple-600 rounded-lg"},[e("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z"})])],-1))])]),e("div",ge,[e("div",he,[e("div",null,[t[17]||(t[17]=e("p",{class:"text-sm text-gray-400"},"Avg Workload",-1)),e("p",pe,l(Math.round((a.technicians.data?.reduce((s,z)=>s+(z.active_orders_count||0),0)||0)/Math.max(a.technicians.data?.length||1,1))),1)]),t[18]||(t[18]=e("div",{class:"p-2 bg-orange-600 rounded-lg"},[e("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"})])],-1))])])]),e("div",fe,[t[28]||(t[28]=e("div",{class:"px-6 py-4 border-b border-gray-700 bg-gradient-to-r from-gray-800 to-gray-700"},[e("div",{class:"flex items-center justify-between"},[e("div",{class:"flex items-center space-x-3"},[e("div",{class:"p-2 bg-red-600 rounded-lg"},[e("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"})])]),e("h3",{class:"text-lg font-semibold text-white"},"Technician Directory")])])],-1)),e("div",ye,[e("table",me,[t[25]||(t[25]=e("thead",{class:"bg-gray-800"},[e("tr",null,[e("th",{class:"px-6 py-4 text-left text-xs font-semibold text-gray-300 uppercase tracking-wider"},"Technician"),e("th",{class:"px-6 py-4 text-left text-xs font-semibold text-gray-300 uppercase tracking-wider"},"Employee ID"),e("th",{class:"px-6 py-4 text-left text-xs font-semibold text-gray-300 uppercase tracking-wider"},"Specialization"),e("th",{class:"px-6 py-4 text-left text-xs font-semibold text-gray-300 uppercase tracking-wider"},"Status"),e("th",{class:"px-6 py-4 text-left text-xs font-semibold text-gray-300 uppercase tracking-wider"},"Workload"),e("th",{class:"px-6 py-4 text-right text-xs font-semibold text-gray-300 uppercase tracking-wider"},"Actions")])],-1)),e("tbody",we,[(r(!0),n(w,null,A(a.technicians.data,s=>(r(),n("tr",{key:s.id,class:"hover:bg-gray-800 transition-colors duration-200 group"},[e("td",be,[e("div",ke,[e("div",_e,[e("span",Ce,l(s.user?.name?.charAt(0)),1)]),e("div",null,[e("div",Me,l(s.user?.name),1),e("div",je,l(s.user?.email),1),s.phone?(r(),n("div",Te,l(s.phone),1)):u("",!0)])])]),e("td",ze,[e("span",Be,l(s.employee_id),1)]),e("td",Ae,[e("span",Se,l(s.specialization),1)]),e("td",De,[e("span",{class:_(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border",U(s.is_active)])},l(s.is_active?"Active":"Inactive"),3)]),e("td",He,[e("span",{class:_(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border",W(s.active_orders_count)])},l(s.active_orders_count||0)+" active ",3)]),e("td",Ve,[e("div",Le,[f(y(C),{href:o.route("technicians.show",s.id),class:"p-2 text-blue-400 hover:text-blue-300 hover:bg-blue-400 hover:bg-opacity-10 rounded-lg transition-all duration-200",title:"View Technician"},{default:m(()=>t[19]||(t[19]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"}),e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"})],-1)])),_:2,__:[19]},1032,["href"]),e("button",{onClick:z=>N(s),class:"p-2 text-yellow-400 hover:text-yellow-300 hover:bg-yellow-400 hover:bg-opacity-10 rounded-lg transition-all duration-200",title:"Edit Technician"},t[20]||(t[20]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})],-1)]),8,$e),e("button",{onClick:z=>E(s),class:_(["p-2 rounded-lg transition-all duration-200",s.is_active?"text-red-400 hover:text-red-300 hover:bg-red-400 hover:bg-opacity-10":"text-green-400 hover:text-green-300 hover:bg-green-400 hover:bg-opacity-10"]),title:s.is_active?"Deactivate Technician":"Reactivate Technician"},[s.is_active?(r(),n("svg",Ne,t[21]||(t[21]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728"},null,-1)]))):(r(),n("svg",Ee,t[22]||(t[22]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"},null,-1)])))],10,Fe)])])]))),128)),a.technicians.data?.length===0?(r(),n("tr",Ie,[e("td",Pe,[e("div",Re,[t[24]||(t[24]=e("div",{class:"w-16 h-16 bg-gray-800 rounded-full flex items-center justify-center mb-4"},[e("svg",{class:"w-8 h-8 text-gray-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"1",d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})])],-1)),e("h3",Ue,l(i.value?"No technicians found":"No technicians yet"),1),e("p",We,l(i.value?`No technicians match "${i.value}"`:"Get started by adding your first technician"),1),i.value?(r(),n("button",{key:1,onClick:L,class:"text-red-400 hover:text-red-300 text-sm font-medium"}," Clear search and show all technicians → ")):(r(),n("button",{key:0,onClick:V,class:"bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 text-white px-6 py-2 rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl flex items-center space-x-2"},t[23]||(t[23]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),e("span",null,"Add your first technician",-1)])))])])])):u("",!0)])])]),a.technicians.links?(r(),n("div",qe,[e("div",Ge,[e("div",Qe,[a.technicians.prev_page_url?(r(),S(y(C),{key:0,href:a.technicians.prev_page_url,class:"relative inline-flex items-center px-4 py-2 border border-gray-600 text-sm font-medium rounded-md text-gray-300 bg-gray-800 hover:bg-gray-700"},{default:m(()=>t[26]||(t[26]=[$(" Previous ",-1)])),_:1,__:[26]},8,["href"])):u("",!0),a.technicians.next_page_url?(r(),S(y(C),{key:1,href:a.technicians.next_page_url,class:"ml-3 relative inline-flex items-center px-4 py-2 border border-gray-600 text-sm font-medium rounded-md text-gray-300 bg-gray-800 hover:bg-gray-700"},{default:m(()=>t[27]||(t[27]=[$(" Next ",-1)])),_:1,__:[27]},8,["href"])):u("",!0)]),e("div",Je,[e("div",null,[e("p",Ke," Showing "+l(a.technicians.from)+" to "+l(a.technicians.to)+" of "+l(a.technicians.total)+" results ",1)]),e("div",null,[e("nav",Oe,[(r(!0),n(w,null,A(a.technicians.links,s=>(r(),n(w,{key:s.label},[s.url?(r(),S(y(C),{key:0,href:s.url,class:_(["relative inline-flex items-center px-2 py-2 border text-sm font-medium",{"z-10 bg-red-600 border-red-600 text-white":s.active,"bg-gray-800 border-gray-600 text-gray-300 hover:bg-gray-700":!s.active}]),innerHTML:s.label},null,8,["href","class","innerHTML"])):(r(),n("span",{key:1,class:"relative inline-flex items-center px-2 py-2 border text-sm font-medium cursor-not-allowed opacity-50 bg-gray-800 border-gray-600 text-gray-500",innerHTML:s.label},null,8,Xe))],64))),128))])])])])])):u("",!0)])]),f(K,{show:h.value,technician:M.value,onClose:t[4]||(t[4]=s=>h.value=!1),onSaved:I},null,8,["show","technician"]),f(O,{show:b.value,processing:p.value,title:d.value?.is_active?"Deactivate Technician":"Reactivate Technician",message:d.value?.is_active?`Are you sure you want to deactivate ${d.value?.user?.name}? They will no longer be able to access the system, but their repair history will be preserved.`:`Are you sure you want to reactivate ${d.value?.user?.name}? They will regain access to the system and can be assigned new repair orders.`,"confirm-text":d.value?.is_active?"Deactivate":"Reactivate","cancel-text":"Cancel",type:d.value?.is_active?"danger":"success",onConfirm:P,onCancel:R},null,8,["show","processing","title","message","confirm-text","type"])]),_:1})],64))}};export{rt as default};
