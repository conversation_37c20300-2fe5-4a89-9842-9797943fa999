<?php

namespace App\Http\Controllers;

use App\Models\Customer;
use App\Models\RepairOrder;
use App\Models\Service;
use App\Models\Invoice;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class NewReportController extends Controller
{
    /**
     * Main Reports Dashboard - Clean and Professional
     */
    public function index()
    {
        return Inertia::render('Reports/NewDashboard', [
            'metrics' => $this->getDashboardMetrics(),
            'charts' => $this->getDashboardCharts(),
            'trends' => $this->getTrendData(),
        ]);
    }

    /**
     * Revenue Analytics Page
     */
    public function revenue(Request $request)
    {
        $period = $request->get('period', '30days');
        $dateRange = $this->getDateRange($period);

        return Inertia::render('Reports/Revenue', [
            'data' => $this->getRevenueData($dateRange),
            'charts' => $this->getRevenueCharts($dateRange),
            'period' => $period,
            'dateRange' => $dateRange,
        ]);
    }

    /**
     * Orders Analytics Page
     */
    public function orders(Request $request)
    {
        $period = $request->get('period', '30days');
        $dateRange = $this->getDateRange($period);

        return Inertia::render('Reports/Orders', [
            'data' => $this->getOrdersData($dateRange),
            'charts' => $this->getOrdersCharts($dateRange),
            'period' => $period,
            'dateRange' => $dateRange,
        ]);
    }

    /**
     * Customers Analytics Page
     */
    public function customers(Request $request)
    {
        $period = $request->get('period', '30days');
        $dateRange = $this->getDateRange($period);

        return Inertia::render('Reports/Customers', [
            'data' => $this->getCustomersData($dateRange),
            'charts' => $this->getCustomersCharts($dateRange),
            'period' => $period,
            'dateRange' => $dateRange,
        ]);
    }

    /**
     * Services Analytics Page
     */
    public function services(Request $request)
    {
        $period = $request->get('period', '30days');
        $dateRange = $this->getDateRange($period);

        return Inertia::render('Reports/Services', [
            'data' => $this->getServicesData($dateRange),
            'charts' => $this->getServicesCharts($dateRange),
            'period' => $period,
            'dateRange' => $dateRange,
        ]);
    }

    /**
     * Get Dashboard Metrics
     */
    private function getDashboardMetrics()
    {
        $now = Carbon::now();
        $startOfMonth = $now->copy()->startOfMonth();
        $startOfLastMonth = $now->copy()->subMonth()->startOfMonth();
        $endOfLastMonth = $now->copy()->subMonth()->endOfMonth();

        // Current metrics
        $totalRevenue = Invoice::where('status', 'paid')->sum('total_amount');
        $totalOrders = RepairOrder::count();
        $totalCustomers = Customer::count();
        $activeRepairs = RepairOrder::whereIn('status', ['pending', 'in_progress', 'waiting_parts'])->count();

        // Monthly comparisons
        $thisMonthRevenue = Invoice::where('status', 'paid')
            ->whereBetween('paid_date', [$startOfMonth, $now])
            ->sum('total_amount');
        
        $lastMonthRevenue = Invoice::where('status', 'paid')
            ->whereBetween('paid_date', [$startOfLastMonth, $endOfLastMonth])
            ->sum('total_amount');

        $thisMonthOrders = RepairOrder::whereBetween('created_at', [$startOfMonth, $now])->count();
        $lastMonthOrders = RepairOrder::whereBetween('created_at', [$startOfLastMonth, $endOfLastMonth])->count();

        $thisMonthCustomers = Customer::whereBetween('created_at', [$startOfMonth, $now])->count();
        $lastMonthCustomers = Customer::whereBetween('created_at', [$startOfLastMonth, $endOfLastMonth])->count();

        return [
            'total_revenue' => $totalRevenue,
            'total_orders' => $totalOrders,
            'total_customers' => $totalCustomers,
            'active_repairs' => $activeRepairs,
            'revenue_growth' => $this->calculateGrowth($thisMonthRevenue, $lastMonthRevenue),
            'orders_growth' => $this->calculateGrowth($thisMonthOrders, $lastMonthOrders),
            'customers_growth' => $this->calculateGrowth($thisMonthCustomers, $lastMonthCustomers),
            'completion_rate' => $this->getCompletionRate(),
        ];
    }

    /**
     * Get Dashboard Charts Data
     */
    private function getDashboardCharts()
    {
        $last30Days = Carbon::now()->subDays(30);
        
        // Daily revenue for last 30 days
        $dailyRevenue = Invoice::where('status', 'paid')
            ->where('paid_date', '>=', $last30Days)
            ->selectRaw('DATE(paid_date) as date, SUM(total_amount) as revenue')
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        // Orders by status
        $ordersByStatus = RepairOrder::selectRaw('status, COUNT(*) as count')
            ->groupBy('status')
            ->pluck('count', 'status');

        // Top services by revenue
        $topServices = DB::table('repair_orders')
            ->join('services', 'repair_orders.service_id', '=', 'services.id')
            ->join('invoices', 'repair_orders.id', '=', 'invoices.repair_order_id')
            ->where('invoices.status', 'paid')
            ->selectRaw('services.name, SUM(invoices.total_amount) as revenue, COUNT(repair_orders.id) as orders')
            ->groupBy('services.id', 'services.name')
            ->orderByDesc('revenue')
            ->limit(5)
            ->get();

        return [
            'daily_revenue' => $dailyRevenue,
            'orders_by_status' => $ordersByStatus,
            'top_services' => $topServices,
        ];
    }

    /**
     * Get Trend Data for Dashboard
     */
    private function getTrendData()
    {
        $last7Days = Carbon::now()->subDays(7);
        
        return [
            'daily_orders' => RepairOrder::where('created_at', '>=', $last7Days)
                ->selectRaw('DATE(created_at) as date, COUNT(*) as count')
                ->groupBy('date')
                ->orderBy('date')
                ->get(),
            'daily_customers' => Customer::where('created_at', '>=', $last7Days)
                ->selectRaw('DATE(created_at) as date, COUNT(*) as count')
                ->groupBy('date')
                ->orderBy('date')
                ->get(),
        ];
    }

    /**
     * Calculate growth percentage
     */
    private function calculateGrowth($current, $previous)
    {
        if ($previous == 0) {
            return $current > 0 ? 100 : 0;
        }
        return (($current - $previous) / $previous) * 100;
    }

    /**
     * Get completion rate
     */
    private function getCompletionRate()
    {
        $totalOrders = RepairOrder::count();
        $completedOrders = RepairOrder::whereIn('status', ['completed', 'delivered'])->count();
        
        return $totalOrders > 0 ? ($completedOrders / $totalOrders) * 100 : 0;
    }

    /**
     * Get date range based on period
     */
    private function getDateRange($period)
    {
        $now = Carbon::now();

        switch ($period) {
            case '7days':
                return ['start' => $now->copy()->subDays(7), 'end' => $now];
            case '30days':
                return ['start' => $now->copy()->subDays(30), 'end' => $now];
            case '90days':
                return ['start' => $now->copy()->subDays(90), 'end' => $now];
            case 'thisyear':
                return ['start' => $now->copy()->startOfYear(), 'end' => $now];
            default:
                return ['start' => $now->copy()->subDays(30), 'end' => $now];
        }
    }

    /**
     * Get Revenue Data for detailed page
     */
    private function getRevenueData($dateRange)
    {
        $totalRevenue = Invoice::where('status', 'paid')
            ->whereBetween('paid_date', [$dateRange['start'], $dateRange['end']])
            ->sum('total_amount');

        $averageDaily = $totalRevenue / max(1, $dateRange['start']->diffInDays($dateRange['end']));

        $topServices = DB::table('repair_orders')
            ->join('services', 'repair_orders.service_id', '=', 'services.id')
            ->join('invoices', 'repair_orders.id', '=', 'invoices.repair_order_id')
            ->where('invoices.status', 'paid')
            ->whereBetween('invoices.paid_date', [$dateRange['start'], $dateRange['end']])
            ->selectRaw('services.name, SUM(invoices.total_amount) as revenue, COUNT(repair_orders.id) as orders')
            ->groupBy('services.id', 'services.name')
            ->orderByDesc('revenue')
            ->get();

        return [
            'total_revenue' => $totalRevenue,
            'average_daily' => $averageDaily,
            'top_services' => $topServices,
            'revenue_sources' => $topServices->count(),
        ];
    }

    /**
     * Get Revenue Charts Data
     */
    private function getRevenueCharts($dateRange)
    {
        // Daily revenue trend
        $dailyRevenue = Invoice::where('status', 'paid')
            ->whereBetween('paid_date', [$dateRange['start'], $dateRange['end']])
            ->selectRaw('DATE(paid_date) as date, SUM(total_amount) as revenue')
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        // Revenue by service
        $revenueByService = DB::table('repair_orders')
            ->join('services', 'repair_orders.service_id', '=', 'services.id')
            ->join('invoices', 'repair_orders.id', '=', 'invoices.repair_order_id')
            ->where('invoices.status', 'paid')
            ->whereBetween('invoices.paid_date', [$dateRange['start'], $dateRange['end']])
            ->selectRaw('services.name, SUM(invoices.total_amount) as revenue')
            ->groupBy('services.id', 'services.name')
            ->orderByDesc('revenue')
            ->limit(10)
            ->get();

        return [
            'daily_trend' => $dailyRevenue,
            'by_service' => $revenueByService,
        ];
    }

    /**
     * Get Orders Data for detailed page
     */
    private function getOrdersData($dateRange)
    {
        $totalOrders = RepairOrder::whereBetween('created_at', [$dateRange['start'], $dateRange['end']])->count();
        $completedOrders = RepairOrder::whereIn('status', ['completed', 'delivered'])
            ->whereBetween('created_at', [$dateRange['start'], $dateRange['end']])
            ->count();

        $ordersByStatus = RepairOrder::whereBetween('created_at', [$dateRange['start'], $dateRange['end']])
            ->selectRaw('status, COUNT(*) as count')
            ->groupBy('status')
            ->get()
            ->pluck('count', 'status');

        $averageOrderValue = Invoice::where('status', 'paid')
            ->whereBetween('created_at', [$dateRange['start'], $dateRange['end']])
            ->avg('total_amount') ?? 0;

        return [
            'total_orders' => $totalOrders,
            'completed_orders' => $completedOrders,
            'completion_rate' => $totalOrders > 0 ? ($completedOrders / $totalOrders) * 100 : 0,
            'orders_by_status' => $ordersByStatus,
            'average_order_value' => $averageOrderValue,
        ];
    }

    /**
     * Get Orders Charts Data
     */
    private function getOrdersCharts($dateRange)
    {
        // Daily orders trend
        $dailyOrders = RepairOrder::whereBetween('created_at', [$dateRange['start'], $dateRange['end']])
            ->selectRaw('DATE(created_at) as date, COUNT(*) as count')
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        return [
            'daily_trend' => $dailyOrders,
        ];
    }

    /**
     * Get Customers Data for detailed page
     */
    private function getCustomersData($dateRange)
    {
        $totalCustomers = Customer::whereBetween('created_at', [$dateRange['start'], $dateRange['end']])->count();
        $newCustomers = Customer::whereBetween('created_at', [$dateRange['start'], $dateRange['end']])->count();

        $topCustomers = DB::table('customers')
            ->join('repair_orders', 'customers.id', '=', 'repair_orders.customer_id')
            ->join('invoices', 'repair_orders.id', '=', 'invoices.repair_order_id')
            ->where('invoices.status', 'paid')
            ->whereBetween('invoices.paid_date', [$dateRange['start'], $dateRange['end']])
            ->selectRaw('customers.id, CONCAT(customers.first_name, " ", customers.last_name) as name, customers.phone, SUM(invoices.total_amount) as revenue, COUNT(repair_orders.id) as orders')
            ->groupBy('customers.id', 'customers.first_name', 'customers.last_name', 'customers.phone')
            ->orderByDesc('revenue')
            ->limit(10)
            ->get();

        return [
            'total_customers' => $totalCustomers,
            'new_customers' => $newCustomers,
            'top_customers' => $topCustomers,
            'customer_retention' => $this->getCustomerRetention($dateRange),
        ];
    }

    /**
     * Get Customers Charts Data
     */
    private function getCustomersCharts($dateRange)
    {
        // Daily customer growth
        $dailyCustomers = Customer::whereBetween('created_at', [$dateRange['start'], $dateRange['end']])
            ->selectRaw('DATE(created_at) as date, COUNT(*) as count')
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        return [
            'daily_growth' => $dailyCustomers,
        ];
    }

    /**
     * Get Services Data for detailed page
     */
    private function getServicesData($dateRange)
    {
        $totalServices = Service::count();
        $activeServices = DB::table('repair_orders')
            ->whereBetween('created_at', [$dateRange['start'], $dateRange['end']])
            ->distinct('service_id')
            ->count();

        $servicePerformance = DB::table('services')
            ->leftJoin('repair_orders', 'services.id', '=', 'repair_orders.service_id')
            ->leftJoin('invoices', 'repair_orders.id', '=', 'invoices.repair_order_id')
            ->whereBetween('repair_orders.created_at', [$dateRange['start'], $dateRange['end']])
            ->selectRaw('services.name, services.base_price as price, COUNT(repair_orders.id) as orders, SUM(CASE WHEN invoices.status = "paid" THEN invoices.total_amount ELSE 0 END) as revenue')
            ->groupBy('services.id', 'services.name', 'services.base_price')
            ->orderByDesc('revenue')
            ->get();

        return [
            'total_services' => $totalServices,
            'active_services' => $activeServices,
            'service_performance' => $servicePerformance,
        ];
    }

    /**
     * Get Services Charts Data
     */
    private function getServicesCharts($dateRange)
    {
        // Service popularity
        $servicePopularity = DB::table('repair_orders')
            ->join('services', 'repair_orders.service_id', '=', 'services.id')
            ->whereBetween('repair_orders.created_at', [$dateRange['start'], $dateRange['end']])
            ->selectRaw('services.name, COUNT(*) as count')
            ->groupBy('services.id', 'services.name')
            ->orderByDesc('count')
            ->limit(10)
            ->get();

        return [
            'popularity' => $servicePopularity,
        ];
    }

    /**
     * Get customer retention rate
     */
    private function getCustomerRetention($dateRange)
    {
        $repeatCustomers = DB::table('customers')
            ->join('repair_orders', 'customers.id', '=', 'repair_orders.customer_id')
            ->whereBetween('repair_orders.created_at', [$dateRange['start'], $dateRange['end']])
            ->groupBy('customers.id')
            ->havingRaw('COUNT(repair_orders.id) > 1')
            ->count();

        $totalCustomers = Customer::whereBetween('created_at', [$dateRange['start'], $dateRange['end']])->count();

        return $totalCustomers > 0 ? ($repeatCustomers / $totalCustomers) * 100 : 0;
    }
}
