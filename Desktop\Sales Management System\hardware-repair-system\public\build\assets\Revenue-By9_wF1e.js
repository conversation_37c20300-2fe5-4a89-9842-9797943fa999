import{_ as k}from"./AuthenticatedLayout-D449e8ZD.js";import{C as p}from"./Chart-D7BRt3Gp.js";import{m,g as i,o as d,a as l,d as x,h as _,w as u,b as e,t as o,F as b,y as C,l as D}from"./app-wnQ52fJE.js";import"./ApplicationLogo-BIrQV_G-.js";import"./DropdownLink-BLptVuux.js";import"./chart-C26Vmg0g.js";const A={class:"flex items-center justify-between"},R={class:"flex items-center space-x-4"},S={class:"flex items-center space-x-3"},B=["value"],F={class:"py-8"},j={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},L={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8"},V={class:"bg-gradient-to-br from-blue-600 to-blue-700 rounded-xl p-6 text-white shadow-lg"},M={class:"flex items-center justify-between"},O={class:"text-3xl font-bold mt-1"},I={class:"bg-gradient-to-br from-green-600 to-green-700 rounded-xl p-6 text-white shadow-lg"},T={class:"flex items-center justify-between"},H={class:"text-3xl font-bold mt-1"},N={class:"bg-gradient-to-br from-purple-600 to-purple-700 rounded-xl p-6 text-white shadow-lg"},U={class:"flex items-center justify-between"},z={class:"text-3xl font-bold mt-1"},W={class:"bg-gradient-to-br from-orange-600 to-orange-700 rounded-xl p-6 text-white shadow-lg"},Z={class:"flex items-center justify-between"},E={class:"text-2xl font-bold mt-1"},P={class:"text-orange-100 text-xs mt-1"},Y={class:"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8"},$={class:"bg-gray-800 rounded-xl p-6 border border-gray-700 shadow-lg"},q={class:"bg-gray-800 rounded-xl p-6 border border-gray-700 shadow-lg"},G={class:"bg-gray-800 rounded-xl border border-gray-700 shadow-lg"},J={class:"overflow-x-auto"},K={class:"w-full"},Q={class:"divide-y divide-gray-700"},X={class:"px-6 py-4 whitespace-nowrap text-sm font-medium text-white"},ee={class:"px-6 py-4 whitespace-nowrap text-sm text-green-400 font-semibold"},te={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-300"},se={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-300"},de={__name:"Revenue",props:{data:{type:Object,default:()=>({})},charts:{type:Object,default:()=>({})},period:{type:String,default:"30days"},dateRange:{type:Object,default:()=>({})}},setup(r){const c=r,n=s=>"₱"+parseFloat(s||0).toLocaleString("en-US",{minimumFractionDigits:2}),v=s=>new Date(s).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"}),f=m(()=>{const s=c.charts.daily_trend||[];return{labels:s.map(t=>v(t.date)),datasets:[{label:"Daily Revenue",data:s.map(t=>parseFloat(t.revenue||0)),borderColor:"rgb(59, 130, 246)",backgroundColor:"rgba(59, 130, 246, 0.1)",tension:.4,fill:!0}]}}),y=m(()=>{const s=c.charts.by_service||[];return{labels:s.map(t=>t.name),datasets:[{label:"Revenue by Service",data:s.map(t=>parseFloat(t.revenue||0)),backgroundColor:["rgba(59, 130, 246, 0.8)","rgba(16, 185, 129, 0.8)","rgba(245, 158, 11, 0.8)","rgba(139, 92, 246, 0.8)","rgba(236, 72, 153, 0.8)","rgba(34, 197, 94, 0.8)","rgba(249, 115, 22, 0.8)","rgba(168, 85, 247, 0.8)","rgba(14, 165, 233, 0.8)","rgba(132, 204, 22, 0.8)"],borderWidth:2}]}}),g={responsive:!0,maintainAspectRatio:!1,plugins:{legend:{display:!0,position:"top",labels:{color:"#D1D5DB",font:{family:"Inter, system-ui, sans-serif"}}},tooltip:{backgroundColor:"#1F2937",titleColor:"#F9FAFB",bodyColor:"#D1D5DB",borderColor:"#374151",borderWidth:1,cornerRadius:8,callbacks:{label:function(s){let t=s.dataset.label||"";return t&&(t+=": "),s.parsed.y!==null&&(t+="₱"+s.parsed.y.toLocaleString("en-US",{minimumFractionDigits:2})),t}}}},scales:{x:{ticks:{color:"#9CA3AF",font:{family:"Inter, system-ui, sans-serif"}},grid:{color:"#374151",borderColor:"#4B5563"}},y:{beginAtZero:!0,ticks:{color:"#9CA3AF",font:{family:"Inter, system-ui, sans-serif"},callback:function(s){return"₱"+s.toLocaleString()}},grid:{color:"#374151",borderColor:"#4B5563"}}}},h={...g,indexAxis:"y",scales:{x:{beginAtZero:!0,ticks:{color:"#9CA3AF",font:{family:"Inter, system-ui, sans-serif"},callback:function(s){return"₱"+s.toLocaleString()}},grid:{color:"#374151",borderColor:"#4B5563"}},y:{ticks:{color:"#9CA3AF",font:{family:"Inter, system-ui, sans-serif"}},grid:{color:"#374151",borderColor:"#4B5563"}}}},w=s=>{window.location.href=route("reports.revenue",{period:s})};return(s,t)=>(d(),i(b,null,[l(x(_),{title:"Revenue Analytics"}),l(k,null,{header:u(()=>[e("div",A,[e("div",R,[l(x(D),{href:s.route("reports.index"),class:"text-gray-400 hover:text-white"},{default:u(()=>t[1]||(t[1]=[e("svg",{class:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 19l-7-7 7-7"})],-1)])),_:1,__:[1]},8,["href"]),t[2]||(t[2]=e("div",null,[e("h2",{class:"text-2xl font-bold leading-tight text-white"}," Revenue Analytics "),e("p",{class:"text-sm text-gray-400 mt-1"}," Detailed revenue analysis and trends ")],-1))]),e("div",S,[e("select",{value:r.period,onChange:t[0]||(t[0]=a=>w(a.target.value)),class:"bg-gray-700 border-gray-600 text-white rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"},t[3]||(t[3]=[e("option",{value:"7days"},"Last 7 Days",-1),e("option",{value:"30days"},"Last 30 Days",-1),e("option",{value:"90days"},"Last 90 Days",-1),e("option",{value:"thisyear"},"This Year",-1)]),40,B)])])]),default:u(()=>[e("div",F,[e("div",j,[e("div",L,[e("div",V,[e("div",M,[e("div",null,[t[4]||(t[4]=e("p",{class:"text-blue-100 text-sm font-medium"},"Total Revenue",-1)),e("p",O,o(n(r.data.total_revenue)),1)]),t[5]||(t[5]=e("div",{class:"p-3 bg-blue-500 bg-opacity-30 rounded-lg"},[e("svg",{class:"w-8 h-8 text-blue-100",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})])],-1))])]),e("div",I,[e("div",T,[e("div",null,[t[6]||(t[6]=e("p",{class:"text-green-100 text-sm font-medium"},"Average Daily",-1)),e("p",H,o(n(r.data.average_daily)),1)]),t[7]||(t[7]=e("div",{class:"p-3 bg-green-500 bg-opacity-30 rounded-lg"},[e("svg",{class:"w-8 h-8 text-green-100",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"})])],-1))])]),e("div",N,[e("div",U,[e("div",null,[t[8]||(t[8]=e("p",{class:"text-purple-100 text-sm font-medium"},"Revenue Sources",-1)),e("p",z,o(r.data.revenue_sources),1)]),t[9]||(t[9]=e("div",{class:"p-3 bg-purple-500 bg-opacity-30 rounded-lg"},[e("svg",{class:"w-8 h-8 text-purple-100",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"})])],-1))])]),e("div",W,[e("div",Z,[e("div",null,[t[10]||(t[10]=e("p",{class:"text-orange-100 text-sm font-medium"},"Top Service Revenue",-1)),e("p",E,o(n(r.data.top_services?.[0]?.revenue||0)),1),e("p",P,o(r.data.top_services?.[0]?.name||"N/A"),1)]),t[11]||(t[11]=e("div",{class:"p-3 bg-orange-500 bg-opacity-30 rounded-lg"},[e("svg",{class:"w-8 h-8 text-orange-100",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"})])],-1))])])]),e("div",Y,[e("div",$,[t[12]||(t[12]=e("h3",{class:"text-lg font-semibold text-white mb-6"},"Revenue Trend",-1)),l(p,{type:"line",data:f.value,options:g,height:400},null,8,["data"])]),e("div",q,[t[13]||(t[13]=e("h3",{class:"text-lg font-semibold text-white mb-6"},"Revenue by Service",-1)),l(p,{type:"bar",data:y.value,options:h,height:400},null,8,["data"])])]),e("div",G,[t[15]||(t[15]=e("div",{class:"p-6 border-b border-gray-700"},[e("h3",{class:"text-lg font-semibold text-white"},"Top Services by Revenue")],-1)),e("div",J,[e("table",K,[t[14]||(t[14]=e("thead",{class:"bg-gray-700"},[e("tr",null,[e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"},"Service"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"},"Revenue"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"},"Orders"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"},"Avg. Order Value")])],-1)),e("tbody",Q,[(d(!0),i(b,null,C(r.data.top_services,a=>(d(),i("tr",{key:a.name,class:"hover:bg-gray-700 transition-colors duration-200"},[e("td",X,o(a.name),1),e("td",ee,o(n(a.revenue)),1),e("td",te,o(a.orders),1),e("td",se,o(n(a.revenue/a.orders)),1)]))),128))])])])])])])]),_:1})],64))}};export{de as default};
