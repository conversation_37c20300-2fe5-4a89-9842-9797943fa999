import{r as h,P as V,m as g,p as B,q as m,g as i,o as r,i as p,b as e,a,w as l,d as v,l as b,F as $,y as A,c as T,n as x,t as d,s as w,f as _,L as j}from"./app-wnQ52fJE.js";import{A as y}from"./ApplicationLogo-BIrQV_G-.js";import{_ as k,a as P}from"./DropdownLink-BLptVuux.js";const N={class:"min-h-screen bg-black"},O={class:"flex items-center justify-between h-16 px-4 bg-gradient-to-r from-red-600 to-red-700 border-b border-red-500"},S={class:"mt-8 px-4 space-y-2"},D=["d"],F={class:"flex-1"},q={class:"font-medium"},E={class:"text-xs opacity-75"},H={class:"absolute bottom-0 left-0 right-0 p-3 border-t border-gray-800"},I={class:"flex items-center space-x-3"},R={class:"flex-shrink-0 w-8 h-8 bg-red-600 rounded-full flex items-center justify-center"},U={class:"text-sm font-medium text-white"},Y={class:"flex-1 min-w-0"},G={class:"text-sm font-medium text-white truncate"},J={class:"lg:pl-64"},K={class:"sticky top-0 z-40 flex h-16 shrink-0 items-center gap-x-4 border-b border-gray-800 bg-gradient-to-r from-gray-900 to-black px-4 shadow-lg sm:gap-x-6 sm:px-6 lg:px-8"},Q={class:"flex lg:hidden items-center space-x-3"},W={class:"flex flex-1 gap-x-4 self-stretch lg:gap-x-6"},X={class:"flex flex-1 items-center"},Z={key:0,class:"flex-1"},ee={class:"flex items-center gap-x-4 lg:gap-x-6"},te={class:"inline-flex rounded-md"},se={type:"button",class:"flex items-center space-x-3 p-2 rounded-lg text-white hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-red-500 transition-colors duration-200"},oe={class:"w-8 h-8 bg-red-600 rounded-full flex items-center justify-center"},ne={class:"text-sm font-medium"},re={class:"hidden sm:block"},ae={class:"text-sm font-medium"},ie={class:"flex-1"},ce={__name:"TechnicianLayout",props:{isAdminView:{type:Boolean,default:!1}},setup(C){const M=C;h(!1);const n=h(!1),c=h(!1),z=V(),u=g(()=>z.props.auth.user);B(()=>{m.on("start",()=>{c.value=!0}),m.on("finish",()=>{c.value=!1})});const L=g(()=>[{name:"Dashboard",href:M.isAdminView?"dashboard.technician-view":"dashboard",icon:"M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z",description:"Your work dashboard"},{name:"My Orders",href:"technician.orders",icon:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z",description:"Assigned repair orders"},{name:"Profile",href:"profile.edit",icon:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z",description:"Update your profile"}]),f=o=>route().current(o);return(o,t)=>(r(),i("div",N,[n.value?(r(),i("div",{key:0,class:"fixed inset-0 z-40 lg:hidden",onClick:t[0]||(t[0]=s=>n.value=!1)},t[4]||(t[4]=[e("div",{class:"fixed inset-0 bg-black opacity-75"},null,-1)]))):p("",!0),e("div",{class:x(["fixed inset-y-0 left-0 z-50 w-64 bg-black border-r border-gray-800 transform transition-transform duration-300 ease-in-out shadow-2xl lg:translate-x-0",{"-translate-x-full":!n.value}])},[e("div",O,[a(v(b),{href:o.route("dashboard"),class:"flex items-center space-x-3"},{default:l(()=>[a(y,{class:"w-8 h-8 text-white"}),t[5]||(t[5]=e("span",{class:"text-xl font-bold text-white"},"TechPanel",-1))]),_:1,__:[5]},8,["href"]),e("button",{onClick:t[1]||(t[1]=s=>n.value=!1),class:"lg:hidden p-1 rounded-md text-white hover:bg-red-800 transition-colors duration-200"},t[6]||(t[6]=[e("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),e("nav",S,[t[7]||(t[7]=e("div",{class:"mb-6"},[e("h3",{class:"px-3 text-xs font-semibold text-gray-400 uppercase tracking-wider"}," Technician Tools ")],-1)),(r(!0),i($,null,A(L.value,s=>(r(),T(v(b),{key:s.name,href:o.route(s.href),class:x(["group flex items-center px-3 py-3 text-sm font-medium text-gray-300 rounded-lg transition-all duration-200 hover:bg-red-600 hover:text-white hover:shadow-lg",{"bg-red-600 text-white shadow-lg":f(s.href)}])},{default:l(()=>[(r(),i("svg",{class:x(["mr-3 h-5 w-5 transition-colors duration-200",{"text-white":f(s.href),"text-gray-400 group-hover:text-white":!f(s.href)}]),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:s.icon},null,8,D)],2)),e("div",F,[e("div",q,d(s.name),1),e("div",E,d(s.description),1)])]),_:2},1032,["href","class"]))),128))]),e("div",H,[e("div",I,[e("div",R,[e("span",U,d(u.value?.name?.charAt(0)),1)]),e("div",Y,[e("p",G,d(u.value?.name),1),t[8]||(t[8]=e("p",{class:"text-xs text-gray-400 truncate"},"Technician",-1))])])])],2),n.value?(r(),i("div",{key:1,class:"fixed inset-0 z-40 lg:hidden",onClick:t[2]||(t[2]=s=>n.value=!1)},t[9]||(t[9]=[e("div",{class:"fixed inset-0 bg-black opacity-75 transition-opacity duration-300"},null,-1)]))):p("",!0),e("div",J,[e("div",K,[e("button",{type:"button",class:"-m-2.5 p-2.5 text-gray-400 hover:text-white lg:hidden transition-colors duration-200",onClick:t[3]||(t[3]=s=>n.value=!n.value)},t[10]||(t[10]=[e("span",{class:"sr-only"},"Open sidebar",-1),e("svg",{class:"h-6 w-6",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5"})],-1)])),e("div",Q,[a(y,{class:"w-6 h-6 text-white"}),t[11]||(t[11]=e("span",{class:"text-lg font-bold text-white"},"TechPanel",-1))]),t[16]||(t[16]=e("div",{class:"h-6 w-px bg-gray-800 lg:hidden"},null,-1)),e("div",W,[e("div",X,[o.$slots.header?(r(),i("header",Z,[w(o.$slots,"header")])):p("",!0)]),e("div",ee,[a(P,{align:"right",width:"48"},{trigger:l(()=>[e("span",te,[e("button",se,[e("div",oe,[e("span",ne,d(u.value?.name?.charAt(0)),1)]),e("div",re,[e("div",ae,d(u.value?.name),1),t[12]||(t[12]=e("div",{class:"text-xs text-gray-400"},"Technician",-1))]),t[13]||(t[13]=e("svg",{class:"ml-2 -mr-0.5 h-4 w-4",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor"},[e("path",{"fill-rule":"evenodd",d:"M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z","clip-rule":"evenodd"})],-1))])])]),content:l(()=>[a(k,{href:o.route("profile.edit")},{default:l(()=>t[14]||(t[14]=[_(" Profile ",-1)])),_:1,__:[14]},8,["href"]),a(k,{href:o.route("logout"),method:"post",as:"button"},{default:l(()=>t[15]||(t[15]=[_(" Log Out ",-1)])),_:1,__:[15]},8,["href"])]),_:1})])])]),e("main",ie,[w(o.$slots,"default")])]),a(j,{show:c.value},null,8,["show"])]))}};export{ce as _};
