import{_ as A}from"./AuthenticatedLayout-D449e8ZD.js";import{_ as L}from"./DeviceModal-C8T7PZy0.js";import{_}from"./RepairOrderModal-C7iRBsj4.js";import{_ as $}from"./ConfirmationModal-D9qANocE.js";import{r as g,m as R,g as r,o as i,a as n,d as u,h as N,w as c,b as e,i as d,t as o,l as m,f as w,F as C,y as O,n as T,q as f}from"./app-wnQ52fJE.js";import"./ApplicationLogo-BIrQV_G-.js";import"./DropdownLink-BLptVuux.js";const E={class:"flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0"},I={class:"flex items-center space-x-4"},F={class:"w-12 h-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg flex items-center justify-center shadow-lg"},U={class:"w-6 h-6 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},q=["d"],P={class:"text-2xl font-bold text-white"},Q={class:"text-gray-400 text-sm"},Y={class:"p-6 space-y-8"},G={class:"grid grid-cols-1 lg:grid-cols-3 gap-8"},J={class:"lg:col-span-2"},K={class:"bg-gradient-to-br from-gray-900 to-gray-800 border border-gray-700 rounded-xl p-6 shadow-xl"},W={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},X={class:"text-white text-lg font-semibold"},Z={class:"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800"},ee={key:0},te={class:"text-white font-mono"},se={key:1},oe={class:"text-white font-mono"},re={key:2},ie={class:"text-white"},le={key:3},ne={class:"text-white"},ae={key:4,class:"md:col-span-2"},de={class:"text-gray-300"},ce={key:5,class:"md:col-span-2"},ue={class:"text-gray-300"},me={key:6,class:"md:col-span-2"},ve={class:"text-gray-300"},xe={class:"space-y-6"},he={class:"bg-gradient-to-br from-gray-900 to-gray-800 border border-gray-700 rounded-xl p-6 shadow-xl"},ge={class:"flex items-center space-x-4"},fe={class:"w-12 h-12 bg-gradient-to-br from-red-500 to-red-600 rounded-full flex items-center justify-center shadow-lg"},be={class:"text-sm font-bold text-white"},we={class:"text-sm text-gray-400"},ye={class:"text-sm text-gray-400"},pe={class:"bg-gradient-to-br from-gray-900 to-gray-800 border border-gray-700 rounded-xl p-6 shadow-xl"},ke={class:"space-y-4"},Ce={class:"flex items-center justify-between"},Me={class:"text-2xl font-bold text-white"},De={class:"flex items-center justify-between"},je={class:"text-sm text-white"},Ve={class:"flex items-center justify-between"},Se={class:"text-sm text-white"},He={class:"bg-gradient-to-br from-gray-900 to-gray-800 border border-gray-700 rounded-xl p-6 shadow-xl"},ze={class:"space-y-3"},Be={class:"bg-gradient-to-br from-gray-900 to-gray-800 border border-gray-700 rounded-xl shadow-xl"},Ae={class:"px-6 py-4 border-b border-gray-700 bg-gradient-to-r from-gray-800 to-gray-700 rounded-t-xl"},Le={class:"flex items-center justify-between"},_e={class:"p-6"},$e={key:0,class:"space-y-4"},Re={class:"flex items-center justify-between mb-2"},Ne={class:"text-sm text-gray-400 mb-2"},Oe={class:"flex items-center justify-between text-xs text-gray-500"},Te={key:0},Ee={key:1,class:"text-center py-8"},Ge={__name:"Show",props:{device:Object,customers:Array,deviceTypes:Array,services:Array,technicians:Array,parts:Array},setup(s){const y=s,v=g(!1),x=g(!1),b=g(!1),h=g(!1),p=R(()=>y.device.repair_orders?.slice(0,5)||[]),M=()=>{v.value=!0},k=()=>{x.value=!0},D=()=>{b.value=!0},j=()=>{v.value=!1,f.reload({only:["device"]})},V=()=>{x.value=!1,f.reload({only:["device"]})},S=()=>{h.value=!0,f.delete(route("devices.destroy",y.device.id),{onSuccess:()=>{f.visit(route("devices.index"))},onError:()=>{h.value=!1}})},H=()=>{b.value=!1,h.value=!1},z=a=>{const t={Smartphone:"M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z",Laptop:"M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z",Desktop:"M9 17v-2m3 2v-4m3 4v-6m2 10H4a2 2 0 01-2-2V5a2 2 0 012-2h16a2 2 0 012 2v14a2 2 0 01-2 2z",Printer:"M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z",Tablet:"M12 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2M3 12l6.414 6.414a2 2 0 001.414.586H19a2 2 0 002-2V7a2 2 0 00-2-2h-7.172a2 2 0 00-1.414.586L3 12z"};return t[a]||t.Smartphone},B=a=>{const t={pending:"bg-yellow-100 text-yellow-800 border-yellow-200",in_progress:"bg-blue-100 text-blue-800 border-blue-200",waiting_parts:"bg-orange-100 text-orange-800 border-orange-200",completed:"bg-green-100 text-green-800 border-green-200",cancelled:"bg-red-100 text-red-800 border-red-200",delivered:"bg-purple-100 text-purple-800 border-purple-200"};return t[a]||t.pending};return(a,t)=>(i(),r(C,null,[n(u(N),{title:`${s.device.brand} ${s.device.model} - Device Details`},null,8,["title"]),n(A,null,{header:c(()=>[e("div",E,[e("div",I,[n(u(m),{href:a.route("devices.index"),class:"p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded-lg transition-colors duration-200"},{default:c(()=>t[2]||(t[2]=[e("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 19l-7-7 7-7"})],-1)])),_:1,__:[2]},8,["href"]),e("div",F,[(i(),r("svg",U,[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:z(s.device.device_type?.name)},null,8,q)]))]),e("div",null,[e("h2",P,o(s.device.brand)+" "+o(s.device.model),1),e("p",Q,o(s.device.device_type?.name)+" • Registered "+o(new Date(s.device.created_at).toLocaleDateString()),1)])]),e("div",{class:"flex items-center space-x-3"},[e("button",{onClick:M,class:"bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center space-x-2"},t[3]||(t[3]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})],-1),e("span",null,"Edit",-1)])),e("button",{onClick:D,class:"bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center space-x-2"},t[4]||(t[4]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})],-1),e("span",null,"Delete",-1)]))])])]),default:c(()=>[e("div",Y,[e("div",G,[e("div",J,[e("div",K,[t[14]||(t[14]=e("div",{class:"flex items-center space-x-3 mb-6"},[e("div",{class:"p-2 bg-purple-600 rounded-lg"},[e("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})])]),e("h3",{class:"text-lg font-semibold text-white"},"Device Information")],-1)),e("div",W,[e("div",null,[t[5]||(t[5]=e("label",{class:"block text-sm font-medium text-gray-400 mb-1"},"Brand & Model",-1)),e("p",X,o(s.device.brand)+" "+o(s.device.model),1)]),e("div",null,[t[6]||(t[6]=e("label",{class:"block text-sm font-medium text-gray-400 mb-1"},"Device Type",-1)),e("span",Z,o(s.device.device_type?.name),1)]),s.device.serial_number?(i(),r("div",ee,[t[7]||(t[7]=e("label",{class:"block text-sm font-medium text-gray-400 mb-1"},"Serial Number",-1)),e("p",te,o(s.device.serial_number),1)])):d("",!0),s.device.imei?(i(),r("div",se,[t[8]||(t[8]=e("label",{class:"block text-sm font-medium text-gray-400 mb-1"},"IMEI",-1)),e("p",oe,o(s.device.imei),1)])):d("",!0),s.device.year?(i(),r("div",re,[t[9]||(t[9]=e("label",{class:"block text-sm font-medium text-gray-400 mb-1"},"Year",-1)),e("p",ie,o(s.device.year),1)])):d("",!0),s.device.color?(i(),r("div",le,[t[10]||(t[10]=e("label",{class:"block text-sm font-medium text-gray-400 mb-1"},"Color",-1)),e("p",ne,o(s.device.color),1)])):d("",!0),s.device.specifications?(i(),r("div",ae,[t[11]||(t[11]=e("label",{class:"block text-sm font-medium text-gray-400 mb-1"},"Specifications",-1)),e("p",de,o(s.device.specifications),1)])):d("",!0),s.device.accessories?(i(),r("div",ce,[t[12]||(t[12]=e("label",{class:"block text-sm font-medium text-gray-400 mb-1"},"Accessories",-1)),e("p",ue,o(s.device.accessories),1)])):d("",!0),s.device.condition_notes?(i(),r("div",me,[t[13]||(t[13]=e("label",{class:"block text-sm font-medium text-gray-400 mb-1"},"Condition Notes",-1)),e("p",ve,o(s.device.condition_notes),1)])):d("",!0)])])]),e("div",xe,[e("div",he,[t[15]||(t[15]=e("div",{class:"flex items-center space-x-3 mb-4"},[e("div",{class:"p-2 bg-blue-600 rounded-lg"},[e("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})])]),e("h3",{class:"text-lg font-semibold text-white"},"Owner")],-1)),e("div",ge,[e("div",fe,[e("span",be,o(s.device.customer?.first_name?.charAt(0))+o(s.device.customer?.last_name?.charAt(0)),1)]),e("div",null,[n(u(m),{href:a.route("customers.show",s.device.customer?.id),class:"text-lg font-semibold text-white hover:text-red-300 transition-colors duration-200"},{default:c(()=>[w(o(s.device.customer?.full_name),1)]),_:1},8,["href"]),e("p",we,o(s.device.customer?.email),1),e("p",ye,o(s.device.customer?.phone),1)])])]),e("div",pe,[t[19]||(t[19]=e("h3",{class:"text-lg font-semibold text-white mb-4"},"Statistics",-1)),e("div",ke,[e("div",Ce,[t[16]||(t[16]=e("span",{class:"text-gray-400"},"Total Repairs",-1)),e("span",Me,o(s.device.repair_orders?.length||0),1)]),e("div",De,[t[17]||(t[17]=e("span",{class:"text-gray-400"},"Registered",-1)),e("span",je,o(new Date(s.device.created_at).toLocaleDateString()),1)]),e("div",Ve,[t[18]||(t[18]=e("span",{class:"text-gray-400"},"Last Updated",-1)),e("span",Se,o(new Date(s.device.updated_at).toLocaleDateString()),1)])])]),e("div",He,[t[22]||(t[22]=e("h3",{class:"text-lg font-semibold text-white mb-4"},"Quick Actions",-1)),e("div",ze,[e("button",{onClick:k,class:"w-full bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center space-x-2"},t[20]||(t[20]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),e("span",null,"Create Repair Order",-1)])),n(u(m),{href:a.route("customers.show",s.device.customer?.id),class:"w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center space-x-2"},{default:c(()=>t[21]||(t[21]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})],-1),e("span",null,"View Customer",-1)])),_:1,__:[21]},8,["href"])])])])]),e("div",Be,[e("div",Ae,[e("div",Le,[t[24]||(t[24]=e("div",{class:"flex items-center space-x-3"},[e("div",{class:"p-2 bg-green-600 rounded-lg"},[e("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})])]),e("h3",{class:"text-lg font-semibold text-white"},"Repair History")],-1)),n(u(m),{href:a.route("repair-orders.index",{device:s.device.id}),class:"text-red-400 hover:text-red-300 text-sm font-medium"},{default:c(()=>t[23]||(t[23]=[w(" View All → ",-1)])),_:1,__:[23]},8,["href"])])]),e("div",_e,[p.value.length>0?(i(),r("div",$e,[(i(!0),r(C,null,O(p.value,l=>(i(),r("div",{key:l.id,class:"bg-gray-800 rounded-lg p-4 hover:bg-gray-750 transition-colors duration-200"},[e("div",Re,[n(u(m),{href:a.route("repair-orders.show",l.id),class:"text-sm font-medium text-white hover:text-red-300 transition-colors duration-200"},{default:c(()=>[w(o(l.order_number),1)]),_:2},1032,["href"]),e("span",{class:T(["inline-flex items-center px-2 py-1 text-xs font-medium rounded-full border",B(l.status)])},o(l.status.replace("_"," ").toUpperCase()),3)]),e("p",Ne,o(l.service?.name),1),e("div",Oe,[e("span",null,o(new Date(l.created_at).toLocaleDateString()),1),l.technician?(i(),r("span",Te,"$"+o(l.total_cost),1)):d("",!0)])]))),128))])):(i(),r("div",Ee,[t[26]||(t[26]=e("svg",{class:"w-12 h-12 text-gray-600 mx-auto mb-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"1",d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})],-1)),t[27]||(t[27]=e("p",{class:"text-gray-400 text-sm mb-4"},"No repair history yet",-1)),e("button",{onClick:k,class:"bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 text-white px-6 py-2 rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl inline-flex items-center space-x-2"},t[25]||(t[25]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),e("span",null,"Create First Repair Order",-1)]))]))])])]),n(L,{show:v.value,device:s.device,customers:s.customers,"device-types":s.deviceTypes,onClose:t[0]||(t[0]=l=>v.value=!1),onSaved:j},null,8,["show","device","customers","device-types"]),n(_,{show:x.value,customers:s.customers,devices:[s.device],services:s.services,technicians:s.technicians,parts:s.parts,"preselected-customer":s.device.customer_id,onClose:t[1]||(t[1]=l=>x.value=!1),onSaved:V},null,8,["show","customers","devices","services","technicians","parts","preselected-customer"]),n($,{show:b.value,processing:h.value,title:"Delete Device",message:`Are you sure you want to delete ${s.device.brand} ${s.device.model}? This action cannot be undone and will also delete all associated repair orders.`,"confirm-text":"Delete Device","cancel-text":"Cancel",type:"danger",onConfirm:S,onCancel:H},null,8,["show","processing","message"])]),_:1})],64))}};export{Ge as default};
