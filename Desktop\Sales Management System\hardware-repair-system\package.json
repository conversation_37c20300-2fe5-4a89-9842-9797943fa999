{"$schema": "https://json.schemastore.org/package.json", "private": true, "type": "module", "scripts": {"build": "vite build", "dev": "vite"}, "devDependencies": {"@inertiajs/vue3": "^2.0.0", "@tailwindcss/forms": "^0.5.3", "@tailwindcss/vite": "^4.0.0", "@vitejs/plugin-vue": "^5.0.0", "autoprefixer": "^10.4.12", "axios": "^1.8.2", "concurrently": "^9.0.1", "laravel-vite-plugin": "^2.0.0", "postcss": "^8.4.31", "tailwindcss": "^3.2.1", "vite": "^7.0.4", "vue": "^3.4.0"}, "dependencies": {"chart.js": "^4.5.0", "chartjs-adapter-date-fns": "^3.0.0", "date-fns": "^4.1.0", "vue-chartjs": "^5.3.2"}}