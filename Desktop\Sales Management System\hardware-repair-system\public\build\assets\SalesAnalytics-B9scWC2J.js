import{_ as j}from"./AuthenticatedLayout-D449e8ZD.js";import{C as y}from"./Chart-D7BRt3Gp.js";import{_ as C,m as g,g as i,o as c,a as u,d as h,h as A,w as x,b as e,t as s,F as m,y as b,J as B,l as D,f as S}from"./app-wnQ52fJE.js";import"./ApplicationLogo-BIrQV_G-.js";import"./DropdownLink-BLptVuux.js";import"./chart-C26Vmg0g.js";const R={class:"flex items-center justify-between"},O={class:"text-sm text-gray-400 mt-1"},P={class:"flex space-x-3"},V={class:"py-12"},M={class:"max-w-7xl mx-auto sm:px-6 lg:px-8 space-y-8"},F={class:"grid grid-cols-1 md:grid-cols-3 gap-6"},L={class:"bg-gradient-to-br from-green-900 to-green-800 border border-green-700 rounded-xl p-6 shadow-xl"},N={class:"flex items-center justify-between"},T={class:"text-3xl font-bold text-white"},z={class:"bg-gradient-to-br from-blue-900 to-blue-800 border border-blue-700 rounded-xl p-6 shadow-xl"},W={class:"flex items-center justify-between"},I={class:"text-3xl font-bold text-white"},U={class:"bg-gradient-to-br from-purple-900 to-purple-800 border border-purple-700 rounded-xl p-6 shadow-xl"},H={class:"flex items-center justify-between"},E={class:"text-3xl font-bold text-white"},J={class:"grid grid-cols-1 lg:grid-cols-2 gap-6"},$={class:"bg-gray-800 rounded-xl p-6 border border-gray-700 shadow-xl"},q={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},G={class:"space-y-4"},K={class:"flex justify-between items-center p-3 bg-gray-700 rounded-lg"},Q={class:"text-white font-semibold text-sm"},X={class:"flex justify-between items-center p-3 bg-gray-700 rounded-lg"},Y={class:"text-white font-semibold text-sm"},Z={class:"pt-3 border-t border-gray-600"},ee={class:"flex justify-between items-center"},te={class:"text-white font-bold"},se={class:"bg-gray-800 rounded-xl p-6 border border-gray-700 shadow-xl"},oe={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},re={class:"space-y-3"},ae={class:"text-white font-medium text-sm"},ne={class:"text-gray-400 text-xs"},le={class:"text-right"},de={class:"text-white font-semibold text-sm"},ie={class:"text-gray-400 text-xs"},ce={class:"bg-gray-800 rounded-xl p-6 border border-gray-700 shadow-xl"},ue={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4"},ge={class:"flex justify-between items-start mb-2"},me={class:"text-white font-medium"},xe={class:"text-sm text-gray-400"},be={class:"text-2xl font-bold text-green-400"},ve={class:"mt-2 bg-gray-600 rounded-full h-2"},pe={class:"grid grid-cols-1 lg:grid-cols-2 gap-6"},_e={class:"bg-gray-800 rounded-xl p-6 border border-gray-700 shadow-xl"},ye={class:"space-y-4"},he={class:"flex justify-between items-center p-4 bg-gray-700 rounded-lg"},fe={class:"text-2xl font-bold text-blue-400"},we={class:"flex justify-between items-center p-4 bg-gray-700 rounded-lg"},ke={class:"text-2xl font-bold text-green-400"},je={class:"pt-4 border-t border-gray-600"},Ce={class:"flex justify-between items-center"},Ae={class:"text-white font-bold"},Be={class:"bg-gray-800 rounded-xl p-6 border border-gray-700 shadow-xl"},De={class:"space-y-3"},Se={class:"flex items-center"},Re={class:"w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-bold text-sm mr-3"},Oe={class:"text-white font-medium"},Pe={class:"text-gray-400 text-sm"},Ve={class:"text-right"},Me={class:"text-white font-semibold"},Fe={class:"text-gray-400 text-sm"},Le={class:"bg-gray-800 rounded-xl p-6 border border-gray-700 shadow-xl"},Ne={class:"grid grid-cols-1 md:grid-cols-3 gap-6"},Te={class:"text-center"},ze={class:"text-3xl font-bold text-white"},We={class:"text-center"},Ie={class:"text-3xl font-bold text-green-400"},Ue={class:"text-center"},He={class:"text-3xl font-bold text-red-400"},Ee={__name:"SalesAnalytics",props:{salesData:{type:Object,default:()=>({total_revenue:0,total_orders:0,average_order_value:0,payment_methods:{},invoices:[]})},revenueBreakdown:{type:Object,default:()=>({labor_revenue:0,parts_revenue:0,service_categories:[]})},customerAnalytics:{type:Object,default:()=>({new_customers:0,returning_customers:0,customer_ltv:[]})},paymentAnalytics:{type:Object,default:()=>({payment_methods:[],payment_timing:{avg_days_to_pay:0,same_day_payments:0,late_payments:0}})},filters:{type:Object,default:()=>({})},dateRange:{type:Object,default:()=>({start:new Date().toISOString(),end:new Date().toISOString()})}},setup(r){const l=r,n=a=>"₱"+parseFloat(a||0).toLocaleString("en-US",{minimumFractionDigits:2}),v=a=>new Date(a).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"}),p=g(()=>{const a=l.salesData?.total_revenue||0,t=l.salesData?.payment_methods||{};return Object.entries(t).map(([o,d])=>({method:o.replace("_"," ").toUpperCase(),count:d?.count||0,total:d?.total||0,percentage:a>0?((d?.total||0)/a*100).toFixed(1):0}))}),f=g(()=>{const a=l.revenueBreakdown?.labor_revenue||0,t=l.revenueBreakdown?.parts_revenue||0,o=a+t;return(l.revenueBreakdown?.service_categories||[]).map(_=>({..._,percentage:o>0?((_?.revenue||0)/o*100).toFixed(1):0}))}),w=g(()=>{const a=p.value;return{labels:a.map(t=>t.method),datasets:[{data:a.map(t=>t.total),backgroundColor:["rgba(59, 130, 246, 0.8)","rgba(16, 185, 129, 0.8)","rgba(245, 158, 11, 0.8)","rgba(239, 68, 68, 0.8)","rgba(139, 92, 246, 0.8)"],borderColor:["rgb(59, 130, 246)","rgb(16, 185, 129)","rgb(245, 158, 11)","rgb(239, 68, 68)","rgb(139, 92, 246)"],borderWidth:2}]}}),k=g(()=>({labels:["Labor Revenue","Parts Revenue"],datasets:[{data:[l.revenueBreakdown.labor_revenue,l.revenueBreakdown.parts_revenue],backgroundColor:["rgba(59, 130, 246, 0.8)","rgba(16, 185, 129, 0.8)"],borderColor:["rgb(59, 130, 246)","rgb(16, 185, 129)"],borderWidth:2}]}));return(a,t)=>(c(),i(m,null,[u(h(A),{title:"Sales Analytics"}),u(j,null,{header:x(()=>[e("div",R,[e("div",null,[t[0]||(t[0]=e("h2",{class:"text-xl font-semibold leading-tight text-white"}," Sales Analytics ",-1)),e("p",O,s(v(r.dateRange?.start))+" - "+s(v(r.dateRange?.end)),1)]),e("div",P,[u(h(D),{href:a.route("reports.index"),class:"bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors duration-200"},{default:x(()=>t[1]||(t[1]=[S(" Back to Dashboard ",-1)])),_:1,__:[1]},8,["href"])])])]),default:x(()=>[e("div",V,[e("div",M,[e("div",F,[e("div",L,[e("div",N,[e("div",null,[t[2]||(t[2]=e("p",{class:"text-sm text-green-200"},"Total Revenue",-1)),e("p",T,s(n(r.salesData?.total_revenue||0)),1)]),t[3]||(t[3]=e("div",{class:"p-3 bg-green-800 rounded-lg"},[e("svg",{class:"w-8 h-8 text-green-200",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})])],-1))])]),e("div",z,[e("div",W,[e("div",null,[t[4]||(t[4]=e("p",{class:"text-sm text-blue-200"},"Total Orders",-1)),e("p",I,s((r.salesData?.total_orders||0).toLocaleString()),1)]),t[5]||(t[5]=e("div",{class:"p-3 bg-blue-800 rounded-lg"},[e("svg",{class:"w-8 h-8 text-blue-200",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})])],-1))])]),e("div",U,[e("div",H,[e("div",null,[t[6]||(t[6]=e("p",{class:"text-sm text-purple-200"},"Average Order Value",-1)),e("p",E,s(n(r.salesData?.average_order_value||0)),1)]),t[7]||(t[7]=e("div",{class:"p-3 bg-purple-800 rounded-lg"},[e("svg",{class:"w-8 h-8 text-purple-200",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"})])],-1))])])]),e("div",J,[e("div",$,[t[11]||(t[11]=e("h3",{class:"text-lg font-semibold text-white mb-6"},"Revenue Breakdown",-1)),e("div",q,[e("div",null,[u(y,{type:"doughnut",data:k.value,height:200},null,8,["data"])]),e("div",G,[e("div",K,[t[8]||(t[8]=e("div",{class:"flex items-center"},[e("div",{class:"w-4 h-4 bg-blue-500 rounded mr-3"}),e("span",{class:"text-white text-sm"},"Labor Revenue")],-1)),e("span",Q,s(n(r.revenueBreakdown?.labor_revenue||0)),1)]),e("div",X,[t[9]||(t[9]=e("div",{class:"flex items-center"},[e("div",{class:"w-4 h-4 bg-green-500 rounded mr-3"}),e("span",{class:"text-white text-sm"},"Parts Revenue")],-1)),e("span",Y,s(n(r.revenueBreakdown?.parts_revenue||0)),1)]),e("div",Z,[e("div",ee,[t[10]||(t[10]=e("span",{class:"text-gray-400 text-sm"},"Total Revenue",-1)),e("span",te,s(n((r.revenueBreakdown?.labor_revenue||0)+(r.revenueBreakdown?.parts_revenue||0))),1)])])])])]),e("div",se,[t[12]||(t[12]=e("h3",{class:"text-lg font-semibold text-white mb-6"},"Payment Methods Distribution",-1)),e("div",oe,[e("div",null,[u(y,{type:"pie",data:w.value,height:200},null,8,["data"])]),e("div",re,[(c(!0),i(m,null,b(p.value,o=>(c(),i("div",{key:o.method,class:"flex justify-between items-center p-2 bg-gray-700 rounded-lg"},[e("div",null,[e("p",ae,s(o.method),1),e("p",ne,s(o.count)+" transactions",1)]),e("div",le,[e("p",de,s(n(o.total)),1),e("p",ie,s(o.percentage)+"%",1)])]))),128))])])])]),e("div",ce,[t[13]||(t[13]=e("h3",{class:"text-lg font-semibold text-white mb-6"},"Service Categories Performance",-1)),e("div",ue,[(c(!0),i(m,null,b(f.value,o=>(c(),i("div",{key:o.category,class:"bg-gray-700 rounded-lg p-4"},[e("div",ge,[e("h4",me,s(o.category),1),e("span",xe,s(o.percentage)+"%",1)]),e("p",be,s(n(o.revenue)),1),e("div",ve,[e("div",{class:"bg-green-500 h-2 rounded-full",style:B({width:o.percentage+"%"})},null,4)])]))),128))])]),e("div",pe,[e("div",_e,[t[17]||(t[17]=e("h3",{class:"text-lg font-semibold text-white mb-6"},"Customer Analytics",-1)),e("div",ye,[e("div",he,[t[14]||(t[14]=e("div",null,[e("p",{class:"text-white font-medium"},"New Customers"),e("p",{class:"text-gray-400 text-sm"},"First-time customers")],-1)),e("span",fe,s(r.customerAnalytics?.new_customers||0),1)]),e("div",we,[t[15]||(t[15]=e("div",null,[e("p",{class:"text-white font-medium"},"Returning Customers"),e("p",{class:"text-gray-400 text-sm"},"Repeat customers")],-1)),e("span",ke,s(r.customerAnalytics?.returning_customers||0),1)]),e("div",je,[e("div",Ce,[t[16]||(t[16]=e("span",{class:"text-gray-400"},"Customer Retention Rate",-1)),e("span",Ae,s((((r.customerAnalytics?.returning_customers||0)/((r.customerAnalytics?.new_customers||0)+(r.customerAnalytics?.returning_customers||0))||1)*100).toFixed(1))+"% ",1)])])])]),e("div",Be,[t[18]||(t[18]=e("h3",{class:"text-lg font-semibold text-white mb-6"},"Top Customers by Value",-1)),e("div",De,[(c(!0),i(m,null,b((r.customerAnalytics?.customer_ltv||[]).slice(0,5),(o,d)=>(c(),i("div",{key:o?.id||d,class:"flex items-center justify-between p-3 bg-gray-700 rounded-lg"},[e("div",Se,[e("div",Re,s(d+1),1),e("div",null,[e("p",Oe,s(o?.first_name||"N/A")+" "+s(o?.last_name||""),1),e("p",Pe,s(o?.total_orders||0)+" orders",1)])]),e("div",Ve,[e("p",Me,s(n(o?.lifetime_value||0)),1),e("p",Fe,s(n(o?.average_order_value||0))+" avg",1)])]))),128))])])]),e("div",Le,[t[25]||(t[25]=e("h3",{class:"text-lg font-semibold text-white mb-6"},"Payment Analytics",-1)),e("div",Ne,[e("div",Te,[t[19]||(t[19]=e("p",{class:"text-gray-400 text-sm"},"Average Days to Pay",-1)),e("p",ze,s(Math.round(r.paymentAnalytics.payment_timing?.avg_days_to_pay||0)),1),t[20]||(t[20]=e("p",{class:"text-gray-400 text-xs"},"days",-1))]),e("div",We,[t[21]||(t[21]=e("p",{class:"text-gray-400 text-sm"},"Same Day Payments",-1)),e("p",Ie,s(r.paymentAnalytics.payment_timing?.same_day_payments||0),1),t[22]||(t[22]=e("p",{class:"text-gray-400 text-xs"},"invoices",-1))]),e("div",Ue,[t[23]||(t[23]=e("p",{class:"text-gray-400 text-sm"},"Late Payments",-1)),e("p",He,s(r.paymentAnalytics.payment_timing?.late_payments||0),1),t[24]||(t[24]=e("p",{class:"text-gray-400 text-xs"},"invoices",-1))])])])])])]),_:1})],64))}},Xe=C(Ee,[["__scopeId","data-v-7c13b643"]]);export{Xe as default};
