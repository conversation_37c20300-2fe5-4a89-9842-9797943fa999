import{r as D,u as j,g as r,o as d,a as x,d as a,h as M,w as p,b as t,i as m,n as I,t as i,l as w,f as T,F as _,y as A,J as N,e as z,j as b,v as y,I as O,q as R}from"./app-wnQ52fJE.js";import{_ as U}from"./AuthenticatedLayout-D449e8ZD.js";import"./ApplicationLogo-BIrQV_G-.js";import"./DropdownLink-BLptVuux.js";const B={class:"flex items-center justify-between"},F={class:"text-xl font-semibold leading-tight text-white"},E={class:"text-sm text-gray-400 mt-1"},V={class:"flex items-center space-x-3"},L={class:"py-12"},H={class:"max-w-4xl mx-auto sm:px-6 lg:px-8"},q={class:"bg-gray-800 rounded-xl shadow-2xl border border-gray-700 overflow-hidden"},G={class:"bg-gray-900 px-6 py-4 border-b border-gray-700"},J={class:"flex items-center justify-between"},Q={class:"p-6"},W={class:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-8"},Y={class:"space-y-4"},K={class:"space-y-2"},X={class:"text-white ml-2"},Z={class:"text-white ml-2"},tt={key:0},et={class:"text-white ml-2"},st={class:"space-y-4"},it={class:"space-y-2"},ot={class:"text-white ml-2"},nt={class:"text-white ml-2"},at={key:0},rt={key:0,class:"mb-8"},dt={class:"bg-gray-900 rounded-lg p-4"},lt={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},ct={class:"text-white ml-2"},mt={class:"text-white ml-2"},ut={key:0,class:"mt-3"},vt={class:"text-white mt-1"},gt={key:1,class:"mt-3"},bt={class:"text-white mt-1"},xt={class:"mb-8"},pt={class:"bg-gray-900 rounded-lg p-6"},yt={class:"mb-6"},ft={class:"flex justify-between items-center mb-2"},ht={class:"text-white"},wt={class:"text-sm text-gray-400 ml-4"},_t={key:0,class:"mb-6"},kt={class:"flex justify-between items-center mb-3"},$t={class:"text-white"},Pt={class:"ml-4 space-y-2"},St={class:"text-gray-300"},Ct={class:"font-medium"},Dt={class:"text-gray-400 ml-2"},jt={class:"text-gray-400 ml-2"},Mt={class:"text-gray-300"},It={class:"border-t border-gray-700 pt-4 mb-4"},Tt={class:"flex justify-between items-center"},At={class:"text-white font-medium"},Nt={class:"bg-gray-900 rounded-lg p-6"},zt={class:"space-y-3"},Ot={class:"flex justify-between"},Rt={class:"text-white"},Ut={key:0,class:"flex justify-between"},Bt={class:"text-gray-400"},Ft={class:"text-white"},Et={key:1,class:"flex justify-between"},Vt={class:"text-red-400"},Lt={class:"border-t border-gray-700 pt-3"},Ht={class:"flex justify-between text-lg font-semibold"},qt={class:"text-green-400"},Gt={key:0,class:"mt-4 pt-4 border-t border-gray-700"},Jt={class:"flex justify-between items-center mb-2"},Qt={class:"text-green-400"},Wt={class:"flex justify-between items-center"},Yt={class:"text-yellow-400"},Kt={class:"mt-3"},Xt={class:"flex justify-between text-sm text-gray-400 mb-1"},Zt={class:"w-full bg-gray-700 rounded-full h-2"},te={key:1,class:"mt-6 bg-green-900/20 border border-green-700 rounded-lg p-4"},ee={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},se={class:"text-white ml-2 capitalize"},ie={class:"text-white ml-2"},oe={key:2,class:"mt-6"},ne={class:"text-gray-300 bg-gray-900 rounded-lg p-4"},ae={key:3,class:"mt-8 flex justify-end"},re={key:0,class:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"},de={class:"bg-gray-800 rounded-lg p-6 w-full max-w-md mx-4"},le={class:"relative"},ce=["max"],me={class:"text-xs text-gray-400 mt-1"},ue={key:0,class:"text-red-400 text-sm mt-1"},ve={key:0,class:"text-red-400 text-sm mt-1"},ge={key:0,class:"text-red-400 text-sm mt-1"},be={key:0,class:"text-red-400 text-sm mt-1"},xe={key:0,class:"text-red-400 text-sm mt-1"},pe={class:"flex justify-end space-x-3 pt-4"},ye=["disabled"],fe={key:0},he={key:1},Pe={__name:"Show",props:{invoice:Object},setup(s){const o=s,f=D(!1),n=j({payment_method:"cash",paid_date:new Date().toISOString().split("T")[0],amount:o.invoice.total_amount-o.invoice.amount_paid,notes:"",reference_number:""}),v={name:"VSMART TUNE UP",address:"P-10 Poblacion Manticao, Misamis Oriental, Philippines",phone:"+63 ************",email:"<EMAIL>",tin:"123-456-789-000",businessPermit:"BP-2024-001"},c=l=>"₱"+parseFloat(l).toLocaleString("en-US",{minimumFractionDigits:2}),g=l=>new Date(l).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"}),k=l=>({pending:"text-yellow-400 bg-yellow-900/50 border-yellow-700",paid:"text-green-400 bg-green-900/50 border-green-700",overdue:"text-red-400 bg-red-900/50 border-red-700",cancelled:"text-gray-400 bg-gray-900/50 border-gray-700"})[l]||"text-gray-400 bg-gray-900/50 border-gray-700",$=()=>{n.amount=o.invoice.total_amount-o.invoice.amount_paid,f.value=!0},h=()=>{f.value=!1,n.reset()},P=()=>{n.post(route("invoices.mark-as-paid",o.invoice.id),{onSuccess:()=>{h(),R.reload({only:["invoice"]})},onError:l=>{console.error("Payment error:",l)}})},S=()=>{const l=window.open("","_blank"),e=C();l.document.write(e),l.document.close(),l.onload=()=>{l.print(),l.close()}},C=()=>`
        <!DOCTYPE html>
        <html>
        <head>
            <title>Official Receipt - ${o.invoice.invoice_number}</title>
            <style>
                * { margin: 0; padding: 0; box-sizing: border-box; }
                body {
                    font-family: 'Arial', sans-serif;
                    font-size: 12px;
                    line-height: 1.4;
                    color: #000;
                    max-width: 800px;
                    margin: 0 auto;
                    padding: 20px;
                }
                .header { text-align: center; margin-bottom: 30px; border-bottom: 2px solid #000; padding-bottom: 20px; }
                .company-name { font-size: 24px; font-weight: bold; margin-bottom: 10px; }
                .company-details { font-size: 11px; margin-bottom: 5px; }
                .receipt-title { font-size: 18px; font-weight: bold; margin-top: 15px; }
                .invoice-info { display: flex; justify-content: space-between; margin: 20px 0; }
                .customer-info { margin-bottom: 20px; }
                .section-title { font-weight: bold; font-size: 14px; margin-bottom: 10px; border-bottom: 1px solid #ccc; padding-bottom: 5px; }
                .breakdown-table { width: 100%; border-collapse: collapse; margin: 20px 0; }
                .breakdown-table th, .breakdown-table td {
                    border: 1px solid #000;
                    padding: 8px;
                    text-align: left;
                }
                .breakdown-table th { background-color: #f0f0f0; font-weight: bold; }
                .breakdown-table .amount { text-align: right; }
                .totals-section { margin-top: 20px; }
                .total-line { display: flex; justify-content: space-between; margin: 5px 0; }
                .final-total { font-weight: bold; font-size: 16px; border-top: 2px solid #000; padding-top: 10px; margin-top: 10px; }
                .footer { margin-top: 40px; text-align: center; font-size: 10px; }
                .signature-section { margin-top: 40px; display: flex; justify-content: space-between; }
                .signature-box { text-align: center; width: 200px; }
                .signature-line { border-bottom: 1px solid #000; margin-bottom: 5px; height: 40px; }
                @media print {
                    body { margin: 0; padding: 15px; }
                    .no-print { display: none; }
                }
            </style>
        </head>
        <body>
            <div class="header">
                <div class="company-name">${v.name}</div>
                <div class="company-details">${v.address}</div>
                <div class="company-details">Phone: ${v.phone} | Email: ${v.email}</div>
                <div class="company-details">TIN: ${v.tin} | Business Permit: ${v.businessPermit}</div>
                <div class="receipt-title">OFFICIAL RECEIPT</div>
            </div>

            <div class="invoice-info">
                <div>
                    <strong>Receipt No:</strong> ${o.invoice.invoice_number}<br>
                    <strong>Date Issued:</strong> ${g(o.invoice.issue_date)}<br>
                    <strong>Due Date:</strong> ${g(o.invoice.due_date)}
                </div>
                <div>
                    <strong>Repair Order:</strong> ${o.invoice.repair_order?.order_number||"N/A"}<br>
                    <strong>Status:</strong> ${o.invoice.status.toUpperCase()}
                </div>
            </div>

            <div class="customer-info">
                <div class="section-title">CUSTOMER INFORMATION</div>
                <strong>Name:</strong> ${o.invoice.customer.first_name} ${o.invoice.customer.last_name}<br>
                <strong>Phone:</strong> ${o.invoice.customer.phone_number}<br>
                ${o.invoice.customer.facebook_link?`<strong>Facebook:</strong> ${o.invoice.customer.facebook_link}<br>`:""}
            </div>

            <div class="service-info">
                <div class="section-title">SERVICE DETAILS</div>
                <strong>Device:</strong> ${o.invoice.repair_order?.device?.brand||""} ${o.invoice.repair_order?.device?.model||""}<br>
                <strong>Service:</strong> ${o.invoice.repair_order?.service?.name||"N/A"}<br>
                ${o.invoice.repair_order?.diagnosis?`<strong>Diagnosis:</strong> ${o.invoice.repair_order.diagnosis}<br>`:""}
                ${o.invoice.repair_order?.solution?`<strong>Solution:</strong> ${o.invoice.repair_order.solution}<br>`:""}
            </div>

            <table class="breakdown-table">
                <thead>
                    <tr>
                        <th>Description</th>
                        <th>Quantity</th>
                        <th>Unit Price</th>
                        <th class="amount">Amount</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>Labor Cost - ${o.invoice.repair_order?.service?.name||"Service"}</td>
                        <td>1</td>
                        <td class="amount">${c(o.invoice.repair_order?.labor_cost||0)}</td>
                        <td class="amount">${c(o.invoice.repair_order?.labor_cost||0)}</td>
                    </tr>
                    ${o.invoice.repair_order?.parts?.map(l=>`
                        <tr>
                            <td>${l.name} (${l.part_number})</td>
                            <td>${l.pivot.quantity_used}</td>
                            <td class="amount">${c(l.pivot.unit_price)}</td>
                            <td class="amount">${c(l.pivot.total_price)}</td>
                        </tr>
                    `).join("")||""}
                </tbody>
            </table>

            <div class="totals-section">
                <div class="total-line">
                    <span>Subtotal:</span>
                    <span>${c(o.invoice.subtotal)}</span>
                </div>
                ${o.invoice.tax_rate>0?`
                    <div class="total-line">
                        <span>Tax (${o.invoice.tax_rate}%):</span>
                        <span>${c(o.invoice.tax_amount)}</span>
                    </div>
                `:""}
                ${o.invoice.discount_amount>0?`
                    <div class="total-line">
                        <span>Discount:</span>
                        <span>-${c(o.invoice.discount_amount)}</span>
                    </div>
                `:""}
                <div class="total-line final-total">
                    <span>TOTAL AMOUNT:</span>
                    <span>${c(o.invoice.total_amount)}</span>
                </div>
            </div>

            

            ${o.invoice.notes?`
                <div style="margin-top: 20px;">
                    <div class="section-title">NOTES</div>
                    ${o.invoice.notes}
                </div>
            `:""}

            <div class="signature-section">
                <div class="signature-box">
                    <div class="signature-line"></div>
                    <div>Customer Signature</div>
                </div>
                <div class="signature-box">
                    <div class="signature-line"></div>
                    <div>Authorized Signature</div>
                </div>
            </div>

            <div class="footer">
                <p>Thank you for choosing ${v.name}!</p>
                <p>This is a computer-generated receipt.</p>
                <p>Generated on: ${new Date().toLocaleString()}</p>
            </div>
        </body>
        </html>
    `;return(l,e)=>(d(),r(_,null,[x(a(M),{title:`Invoice ${s.invoice.invoice_number}`},null,8,["title"]),x(U,null,{header:p(()=>[t("div",B,[t("div",null,[t("h2",F," Invoice "+i(s.invoice.invoice_number),1),t("p",E," Created "+i(g(s.invoice.created_at)),1)]),t("div",V,[t("button",{onClick:S,class:"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center space-x-2"},e[5]||(e[5]=[t("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z"})],-1),t("span",null,"Print Receipt",-1)])),x(a(w),{href:l.route("invoices.index"),class:"bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center space-x-2"},{default:p(()=>e[6]||(e[6]=[t("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M10 19l-7-7m0 0l7-7m-7 7h18"})],-1),t("span",null,"Back to Invoices",-1)])),_:1,__:[6]},8,["href"])])])]),default:p(()=>[t("div",L,[t("div",H,[t("div",q,[t("div",G,[t("div",J,[e[7]||(e[7]=t("div",null,[t("h3",{class:"text-lg font-semibold text-white"},"Invoice Details")],-1)),t("div",null,[t("span",{class:I(["inline-flex items-center px-3 py-1 text-sm font-medium rounded-full border",k(s.invoice.status)])},i(s.invoice.status.charAt(0).toUpperCase()+s.invoice.status.slice(1)),3)])])]),t("div",Q,[t("div",W,[t("div",Y,[e[11]||(e[11]=t("h4",{class:"text-lg font-medium text-white border-b border-gray-700 pb-2"},"Customer Information",-1)),t("div",K,[t("div",null,[e[8]||(e[8]=t("span",{class:"text-sm text-gray-400"},"Name:",-1)),t("span",X,i(s.invoice.customer.first_name)+" "+i(s.invoice.customer.last_name),1)]),t("div",null,[e[9]||(e[9]=t("span",{class:"text-sm text-gray-400"},"Phone:",-1)),t("span",Z,i(s.invoice.customer.phone_number),1)]),s.invoice.customer.facebook_link?(d(),r("div",tt,[e[10]||(e[10]=t("span",{class:"text-sm text-gray-400"},"Facebook:",-1)),t("span",et,i(s.invoice.customer.facebook_link),1)])):m("",!0)])]),t("div",st,[e[15]||(e[15]=t("h4",{class:"text-lg font-medium text-white border-b border-gray-700 pb-2"},"Invoice Information",-1)),t("div",it,[t("div",null,[e[12]||(e[12]=t("span",{class:"text-sm text-gray-400"},"Issue Date:",-1)),t("span",ot,i(g(s.invoice.issue_date)),1)]),t("div",null,[e[13]||(e[13]=t("span",{class:"text-sm text-gray-400"},"Due Date:",-1)),t("span",nt,i(g(s.invoice.due_date)),1)]),s.invoice.repair_order?(d(),r("div",at,[e[14]||(e[14]=t("span",{class:"text-sm text-gray-400"},"Repair Order:",-1)),x(a(w),{href:l.route("repair-orders.show",s.invoice.repair_order.id),class:"text-blue-400 hover:text-blue-300 ml-2"},{default:p(()=>[T(i(s.invoice.repair_order.order_number),1)]),_:1},8,["href"])])):m("",!0)])])]),s.invoice.repair_order?(d(),r("div",rt,[e[20]||(e[20]=t("h4",{class:"text-lg font-medium text-white border-b border-gray-700 pb-2 mb-4"},"Service Details",-1)),t("div",dt,[t("div",lt,[t("div",null,[e[16]||(e[16]=t("span",{class:"text-sm text-gray-400"},"Device:",-1)),t("span",ct,i(s.invoice.repair_order.device?.brand)+" "+i(s.invoice.repair_order.device?.model),1)]),t("div",null,[e[17]||(e[17]=t("span",{class:"text-sm text-gray-400"},"Service:",-1)),t("span",mt,i(s.invoice.repair_order.service?.name),1)])]),s.invoice.repair_order.diagnosis?(d(),r("div",ut,[e[18]||(e[18]=t("span",{class:"text-sm text-gray-400"},"Diagnosis:",-1)),t("p",vt,i(s.invoice.repair_order.diagnosis),1)])):m("",!0),s.invoice.repair_order.solution?(d(),r("div",gt,[e[19]||(e[19]=t("span",{class:"text-sm text-gray-400"},"Solution:",-1)),t("p",bt,i(s.invoice.repair_order.solution),1)])):m("",!0)])])):m("",!0),t("div",xt,[e[24]||(e[24]=t("h4",{class:"text-lg font-medium text-white border-b border-gray-700 pb-2 mb-4"},"Cost Breakdown",-1)),t("div",pt,[t("div",yt,[t("div",ft,[e[21]||(e[21]=t("span",{class:"text-white font-medium"},"Labor Cost",-1)),t("span",ht,i(c(s.invoice.repair_order?.labor_cost||0)),1)]),t("div",wt," Service: "+i(s.invoice.repair_order?.service?.name),1)]),s.invoice.repair_order?.parts&&s.invoice.repair_order.parts.length>0?(d(),r("div",_t,[t("div",kt,[e[22]||(e[22]=t("span",{class:"text-white font-medium"},"Parts Cost",-1)),t("span",$t,i(c(s.invoice.repair_order?.parts_cost||0)),1)]),t("div",Pt,[(d(!0),r(_,null,A(s.invoice.repair_order.parts,u=>(d(),r("div",{key:u.id,class:"flex justify-between items-center text-sm"},[t("div",St,[t("span",Ct,i(u.name),1),t("span",Dt,"("+i(u.part_number)+")",1),t("span",jt,"× "+i(u.pivot.quantity_used),1)]),t("span",Mt,i(c(u.pivot.total_price)),1)]))),128))])])):m("",!0),t("div",It,[t("div",Tt,[e[23]||(e[23]=t("span",{class:"text-white font-medium"},"Subtotal",-1)),t("span",At,i(c(s.invoice.subtotal)),1)])])])]),t("div",Nt,[e[31]||(e[31]=t("h4",{class:"text-lg font-medium text-white border-b border-gray-700 pb-2 mb-4"},"Financial Summary",-1)),t("div",zt,[t("div",Ot,[e[25]||(e[25]=t("span",{class:"text-gray-400"},"Subtotal:",-1)),t("span",Rt,i(c(s.invoice.subtotal)),1)]),s.invoice.tax_rate>0?(d(),r("div",Ut,[t("span",Bt,"Tax ("+i(s.invoice.tax_rate)+"%):",1),t("span",Ft,i(c(s.invoice.tax_amount)),1)])):m("",!0),s.invoice.discount_amount>0?(d(),r("div",Et,[e[26]||(e[26]=t("span",{class:"text-gray-400"},"Discount:",-1)),t("span",Vt,"-"+i(c(s.invoice.discount_amount)),1)])):m("",!0),t("div",Lt,[t("div",Ht,[e[27]||(e[27]=t("span",{class:"text-white"},"Total Amount:",-1)),t("span",qt,i(c(s.invoice.total_amount)),1)]),s.invoice.amount_paid>0?(d(),r("div",Gt,[t("div",Jt,[e[28]||(e[28]=t("span",{class:"text-gray-300"},"Amount Paid:",-1)),t("span",Qt,i(c(s.invoice.amount_paid)),1)]),t("div",Wt,[e[29]||(e[29]=t("span",{class:"text-gray-300"},"Balance Due:",-1)),t("span",Yt,i(c(s.invoice.total_amount-s.invoice.amount_paid)),1)]),t("div",Kt,[t("div",Xt,[e[30]||(e[30]=t("span",null,"Payment Progress",-1)),t("span",null,i(Math.round(s.invoice.amount_paid/s.invoice.total_amount*100))+"%",1)]),t("div",Zt,[t("div",{class:"bg-green-500 h-2 rounded-full transition-all duration-300",style:N({width:s.invoice.amount_paid/s.invoice.total_amount*100+"%"})},null,4)])])])):m("",!0)])])]),s.invoice.status==="paid"?(d(),r("div",te,[e[34]||(e[34]=t("h4",{class:"text-lg font-medium text-green-400 mb-3"},"Payment Information",-1)),t("div",ee,[t("div",null,[e[32]||(e[32]=t("span",{class:"text-sm text-gray-400"},"Payment Method:",-1)),t("span",se,i(s.invoice.payment_method?.replace("_"," ")),1)]),t("div",null,[e[33]||(e[33]=t("span",{class:"text-sm text-gray-400"},"Payment Date:",-1)),t("span",ie,i(g(s.invoice.paid_date)),1)])])])):m("",!0),s.invoice.notes?(d(),r("div",oe,[e[35]||(e[35]=t("h4",{class:"text-lg font-medium text-white border-b border-gray-700 pb-2 mb-3"},"Notes",-1)),t("p",ne,i(s.invoice.notes),1)])):m("",!0),s.invoice.status==="pending"||s.invoice.status==="partially_paid"?(d(),r("div",ae,[t("button",{onClick:$,class:"bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-lg transition-colors duration-200 flex items-center space-x-2"},[e[36]||(e[36]=t("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1)),t("span",null,i(s.invoice.status==="partially_paid"?"Add Payment":"Mark as Paid"),1)])])):m("",!0)])])])]),f.value?(d(),r("div",re,[t("div",de,[t("div",{class:"flex items-center justify-between mb-4"},[e[38]||(e[38]=t("h3",{class:"text-lg font-semibold text-white"},"Add Payment",-1)),t("button",{onClick:h,class:"text-gray-400 hover:text-white"},e[37]||(e[37]=[t("svg",{class:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),t("form",{onSubmit:z(P,["prevent"]),class:"space-y-4"},[t("div",null,[e[40]||(e[40]=t("label",{class:"block text-sm font-medium text-gray-300 mb-2"},"Payment Amount",-1)),t("div",le,[e[39]||(e[39]=t("span",{class:"absolute left-3 top-2 text-gray-400"},"₱",-1)),b(t("input",{type:"number","onUpdate:modelValue":e[0]||(e[0]=u=>a(n).amount=u),step:"0.01",min:"0.01",max:s.invoice.total_amount-s.invoice.amount_paid,class:"w-full bg-gray-700 border border-gray-600 rounded-lg pl-8 pr-3 py-2 text-white focus:ring-2 focus:ring-red-500 focus:border-transparent"},null,8,ce),[[y,a(n).amount]])]),t("div",me," Remaining balance: ₱"+i((s.invoice.total_amount-s.invoice.amount_paid).toFixed(2)),1),a(n).errors.amount?(d(),r("div",ue,i(a(n).errors.amount),1)):m("",!0)]),t("div",null,[e[42]||(e[42]=t("label",{class:"block text-sm font-medium text-gray-300 mb-2"},"Payment Method",-1)),b(t("select",{"onUpdate:modelValue":e[1]||(e[1]=u=>a(n).payment_method=u),class:"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:ring-2 focus:ring-red-500 focus:border-transparent"},e[41]||(e[41]=[t("option",{value:"cash"},"Cash",-1),t("option",{value:"card"},"Gcash/Maya",-1),t("option",{value:"bank_transfer"},"Bank Transfer",-1),t("option",{value:"check"},"Check",-1)]),512),[[O,a(n).payment_method]]),a(n).errors.payment_method?(d(),r("div",ve,i(a(n).errors.payment_method),1)):m("",!0)]),t("div",null,[e[43]||(e[43]=t("label",{class:"block text-sm font-medium text-gray-300 mb-2"},"Payment Date",-1)),b(t("input",{type:"date","onUpdate:modelValue":e[2]||(e[2]=u=>a(n).paid_date=u),class:"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:ring-2 focus:ring-red-500 focus:border-transparent"},null,512),[[y,a(n).paid_date]]),a(n).errors.paid_date?(d(),r("div",ge,i(a(n).errors.paid_date),1)):m("",!0)]),t("div",null,[e[44]||(e[44]=t("label",{class:"block text-sm font-medium text-gray-300 mb-2"},"Reference Number (Optional)",-1)),b(t("input",{type:"text","onUpdate:modelValue":e[3]||(e[3]=u=>a(n).reference_number=u),placeholder:"Check number, transaction ID, etc.",class:"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:ring-2 focus:ring-red-500 focus:border-transparent"},null,512),[[y,a(n).reference_number]]),a(n).errors.reference_number?(d(),r("div",be,i(a(n).errors.reference_number),1)):m("",!0)]),t("div",null,[e[45]||(e[45]=t("label",{class:"block text-sm font-medium text-gray-300 mb-2"},"Notes (Optional)",-1)),b(t("textarea",{"onUpdate:modelValue":e[4]||(e[4]=u=>a(n).notes=u),rows:"2",placeholder:"Additional payment notes...",class:"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:ring-2 focus:ring-red-500 focus:border-transparent resize-none"},null,512),[[y,a(n).notes]]),a(n).errors.notes?(d(),r("div",xe,i(a(n).errors.notes),1)):m("",!0)]),t("div",pe,[t("button",{type:"button",onClick:h,class:"px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors duration-200"}," Cancel "),t("button",{type:"submit",disabled:a(n).processing,class:"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors duration-200 disabled:opacity-50"},[a(n).processing?(d(),r("span",fe,"Processing...")):(d(),r("span",he,"Add Payment"))],8,ye)])],32)])])):m("",!0)]),_:1})],64))}};export{Pe as default};
