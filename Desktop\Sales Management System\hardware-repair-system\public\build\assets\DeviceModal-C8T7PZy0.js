import{u as _,r as C,x as V,g as i,i as n,o as d,b as t,e as y,t as l,j as c,I as v,d as o,F as x,y as w,v as u,f as D}from"./app-wnQ52fJE.js";const M={class:"flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0"},U={class:"flex items-center justify-between mb-6"},S={class:"flex items-center space-x-3"},j={class:"text-xl font-bold text-white"},B={class:"text-sm text-gray-400"},E={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},A=["value"],F={key:0,class:"mt-1 text-sm text-red-400"},N=["value"],z={key:0,class:"mt-1 text-sm text-red-400"},I={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},q={key:0,class:"mt-1 text-sm text-red-400"},P={key:0,class:"mt-1 text-sm text-red-400"},T={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},H={key:0,class:"mt-1 text-sm text-red-400"},R={key:0,class:"mt-1 text-sm text-red-400"},G={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},L=["max"],Y={key:0,class:"mt-1 text-sm text-red-400"},O={key:0,class:"mt-1 text-sm text-red-400"},W={key:0,class:"mt-1 text-sm text-red-400"},J={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},K={key:0,class:"mt-1 text-sm text-red-400"},Q={key:0,class:"mt-1 text-sm text-red-400"},X={class:"flex items-center justify-end space-x-4 pt-6 border-t border-gray-700"},Z=["disabled"],$={key:0,class:"flex items-center"},ee={key:1},re={__name:"DeviceModal",props:{show:{type:Boolean,default:!1},device:{type:Object,default:null},customers:{type:Array,default:()=>[]},deviceTypes:{type:Array,default:()=>[]},preselectedCustomer:{type:[String,Number],default:null}},emits:["close","saved"],setup(f,{emit:h}){const a=f,p=h,e=_({customer_id:"",device_type_id:"",brand:"",model:"",serial_number:"",imei:"",year:"",color:"",specifications:"",accessories:"",condition_notes:""}),m=C(!1);V(()=>a.show,g=>{g&&(a.device?(m.value=!0,e.customer_id=a.device.customer_id||"",e.device_type_id=a.device.device_type_id||"",e.brand=a.device.brand||"",e.model=a.device.model||"",e.serial_number=a.device.serial_number||"",e.imei=a.device.imei||"",e.year=a.device.year||"",e.color=a.device.color||"",e.specifications=a.device.specifications||"",e.accessories=a.device.accessories||"",e.condition_notes=a.device.condition_notes||""):(m.value=!1,e.reset(),a.preselectedCustomer&&(e.customer_id=a.preselectedCustomer)))});const k=()=>{if(!e){console.error("Form object is not initialized");return}m.value?e.put(route("devices.update",a.device.id),{onSuccess:()=>{window.toast&&window.toast.success("Device updated successfully!"),p("saved"),b()},onError:g=>{console.error("Device update failed:",g),window.toast&&window.toast.error("Failed to update device. Please try again.")}}):e.post(route("devices.store"),{onSuccess:()=>{window.toast&&window.toast.success("Device created successfully!"),p("saved"),b()},onError:g=>{console.error("Device creation failed:",g),window.toast&&window.toast.error("Failed to create device. Please try again.")}})},b=()=>{e&&(e.reset(),e.clearErrors()),p("close")};return(g,r)=>f.show?(d(),i("div",{key:0,class:"fixed inset-0 z-50 overflow-y-auto",onClick:b},[t("div",M,[r[28]||(r[28]=t("div",{class:"fixed inset-0 transition-opacity bg-black bg-opacity-75 backdrop-blur-sm"},null,-1)),t("div",{class:"inline-block w-full max-w-4xl p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-gradient-to-br from-gray-900 to-gray-800 border border-gray-700 shadow-2xl rounded-2xl",onClick:r[11]||(r[11]=y(()=>{},["stop"]))},[t("div",U,[t("div",S,[r[12]||(r[12]=t("div",{class:"p-2 bg-purple-600 rounded-lg"},[t("svg",{class:"w-6 h-6 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"})])],-1)),t("div",null,[t("h3",j,l(m.value?"Edit Device":"Register New Device"),1),t("p",B,l(m.value?"Update device information":"Add a new device to the system"),1)])]),t("button",{onClick:b,class:"p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded-lg transition-colors duration-200"},r[13]||(r[13]=[t("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),t("form",{onSubmit:y(k,["prevent"]),class:"space-y-6"},[t("div",E,[t("div",null,[r[15]||(r[15]=t("label",{for:"customer_id",class:"block text-sm font-medium text-gray-300 mb-2"}," Customer * ",-1)),c(t("select",{id:"customer_id","onUpdate:modelValue":r[0]||(r[0]=s=>o(e).customer_id=s),class:"w-full rounded-lg bg-gray-800 border-gray-700 text-white shadow-sm focus:border-red-500 focus:ring-red-500 transition-colors duration-200",required:""},[r[14]||(r[14]=t("option",{value:"",class:"bg-gray-800 text-white"},"Select customer...",-1)),(d(!0),i(x,null,w(f.customers,s=>(d(),i("option",{key:s.id,value:s.id,class:"bg-gray-800 text-white"},l(s.full_name),9,A))),128))],512),[[v,o(e).customer_id]]),o(e).errors.customer_id?(d(),i("div",F,l(o(e).errors.customer_id),1)):n("",!0)]),t("div",null,[r[17]||(r[17]=t("label",{for:"device_type_id",class:"block text-sm font-medium text-gray-300 mb-2"}," Device Category * ",-1)),c(t("select",{id:"device_type_id","onUpdate:modelValue":r[1]||(r[1]=s=>o(e).device_type_id=s),class:"w-full rounded-lg bg-gray-800 border-gray-700 text-white shadow-sm focus:border-red-500 focus:ring-red-500 transition-colors duration-200",required:""},[r[16]||(r[16]=t("option",{value:"",class:"bg-gray-800 text-white"},"Select device category...",-1)),(d(!0),i(x,null,w(f.deviceTypes,s=>(d(),i("option",{key:s.id,value:s.id,class:"bg-gray-800 text-white"},l(s.name)+" ("+l(s.category)+") ",9,N))),128))],512),[[v,o(e).device_type_id]]),o(e).errors.device_type_id?(d(),i("div",z,l(o(e).errors.device_type_id),1)):n("",!0)])]),t("div",I,[t("div",null,[r[18]||(r[18]=t("label",{for:"brand",class:"block text-sm font-medium text-gray-300 mb-2"}," Brand * ",-1)),c(t("input",{id:"brand","onUpdate:modelValue":r[2]||(r[2]=s=>o(e).brand=s),type:"text",class:"w-full rounded-lg bg-gray-800 border-gray-700 text-white shadow-sm focus:border-red-500 focus:ring-red-500 transition-colors duration-200",placeholder:"e.g., Apple, Samsung, HP",required:""},null,512),[[u,o(e).brand]]),o(e).errors.brand?(d(),i("div",q,l(o(e).errors.brand),1)):n("",!0)]),t("div",null,[r[19]||(r[19]=t("label",{for:"model",class:"block text-sm font-medium text-gray-300 mb-2"}," Model * ",-1)),c(t("input",{id:"model","onUpdate:modelValue":r[3]||(r[3]=s=>o(e).model=s),type:"text",class:"w-full rounded-lg bg-gray-800 border-gray-700 text-white shadow-sm focus:border-red-500 focus:ring-red-500 transition-colors duration-200",placeholder:"e.g., iPhone 14, Galaxy S23",required:""},null,512),[[u,o(e).model]]),o(e).errors.model?(d(),i("div",P,l(o(e).errors.model),1)):n("",!0)])]),t("div",T,[t("div",null,[r[20]||(r[20]=t("label",{for:"serial_number",class:"block text-sm font-medium text-gray-300 mb-2"}," Serial Number ",-1)),c(t("input",{id:"serial_number","onUpdate:modelValue":r[4]||(r[4]=s=>o(e).serial_number=s),type:"text",class:"w-full rounded-lg bg-gray-800 border-gray-700 text-white shadow-sm focus:border-red-500 focus:ring-red-500 transition-colors duration-200",placeholder:"Device serial number"},null,512),[[u,o(e).serial_number]]),o(e).errors.serial_number?(d(),i("div",H,l(o(e).errors.serial_number),1)):n("",!0)]),t("div",null,[r[21]||(r[21]=t("label",{for:"imei",class:"block text-sm font-medium text-gray-300 mb-2"}," IMEI (for phones) ",-1)),c(t("input",{id:"imei","onUpdate:modelValue":r[5]||(r[5]=s=>o(e).imei=s),type:"text",class:"w-full rounded-lg bg-gray-800 border-gray-700 text-white shadow-sm focus:border-red-500 focus:ring-red-500 transition-colors duration-200",placeholder:"15-digit IMEI number"},null,512),[[u,o(e).imei]]),o(e).errors.imei?(d(),i("div",R,l(o(e).errors.imei),1)):n("",!0)])]),t("div",G,[t("div",null,[r[22]||(r[22]=t("label",{for:"year",class:"block text-sm font-medium text-gray-300 mb-2"}," Year ",-1)),c(t("input",{id:"year","onUpdate:modelValue":r[6]||(r[6]=s=>o(e).year=s),type:"number",min:"1990",max:new Date().getFullYear()+1,class:"w-full rounded-lg bg-gray-800 border-gray-700 text-white shadow-sm focus:border-red-500 focus:ring-red-500 transition-colors duration-200",placeholder:"2023"},null,8,L),[[u,o(e).year]]),o(e).errors.year?(d(),i("div",Y,l(o(e).errors.year),1)):n("",!0)]),t("div",null,[r[23]||(r[23]=t("label",{for:"color",class:"block text-sm font-medium text-gray-300 mb-2"}," Color ",-1)),c(t("input",{id:"color","onUpdate:modelValue":r[7]||(r[7]=s=>o(e).color=s),type:"text",class:"w-full rounded-lg bg-gray-800 border-gray-700 text-white shadow-sm focus:border-red-500 focus:ring-red-500 transition-colors duration-200",placeholder:"e.g., Space Gray, White"},null,512),[[u,o(e).color]]),o(e).errors.color?(d(),i("div",O,l(o(e).errors.color),1)):n("",!0)])]),t("div",null,[r[24]||(r[24]=t("label",{for:"specifications",class:"block text-sm font-medium text-gray-300 mb-2"}," Specifications ",-1)),c(t("textarea",{id:"specifications","onUpdate:modelValue":r[8]||(r[8]=s=>o(e).specifications=s),rows:"3",class:"w-full rounded-lg bg-gray-800 border-gray-700 text-white shadow-sm focus:border-red-500 focus:ring-red-500 transition-colors duration-200",placeholder:"Device specifications, storage, RAM, etc."},null,512),[[u,o(e).specifications]]),o(e).errors.specifications?(d(),i("div",W,l(o(e).errors.specifications),1)):n("",!0)]),t("div",J,[t("div",null,[r[25]||(r[25]=t("label",{for:"accessories",class:"block text-sm font-medium text-gray-300 mb-2"}," Accessories ",-1)),c(t("textarea",{id:"accessories","onUpdate:modelValue":r[9]||(r[9]=s=>o(e).accessories=s),rows:"3",class:"w-full rounded-lg bg-gray-800 border-gray-700 text-white shadow-sm focus:border-red-500 focus:ring-red-500 transition-colors duration-200",placeholder:"Charger, case, headphones, etc."},null,512),[[u,o(e).accessories]]),o(e).errors.accessories?(d(),i("div",K,l(o(e).errors.accessories),1)):n("",!0)]),t("div",null,[r[26]||(r[26]=t("label",{for:"condition_notes",class:"block text-sm font-medium text-gray-300 mb-2"}," Condition Notes ",-1)),c(t("textarea",{id:"condition_notes","onUpdate:modelValue":r[10]||(r[10]=s=>o(e).condition_notes=s),rows:"3",class:"w-full rounded-lg bg-gray-800 border-gray-700 text-white shadow-sm focus:border-red-500 focus:ring-red-500 transition-colors duration-200",placeholder:"Current condition, existing damage, etc."},null,512),[[u,o(e).condition_notes]]),o(e).errors.condition_notes?(d(),i("div",Q,l(o(e).errors.condition_notes),1)):n("",!0)])]),t("div",X,[t("button",{type:"button",onClick:b,class:"px-6 py-2 border border-gray-600 rounded-lg text-gray-300 hover:bg-gray-700 transition-colors duration-200 font-medium"}," Cancel "),t("button",{type:"submit",disabled:o(e).processing,class:"px-6 py-2 bg-gradient-to-r from-red-600 to-red-700 text-white rounded-lg hover:from-red-700 hover:to-red-800 disabled:opacity-50 transition-all duration-200 shadow-lg hover:shadow-xl font-medium"},[o(e).processing?(d(),i("span",$,[r[27]||(r[27]=t("svg",{class:"animate-spin -ml-1 mr-2 h-4 w-4 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"},[t("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"}),t("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})],-1)),D(" "+l(m.value?"Updating...":"Creating..."),1)])):(d(),i("span",ee,l(m.value?"Update Device":"Register Device"),1))],8,Z)])],32)])])])):n("",!0)}};export{re as _};
