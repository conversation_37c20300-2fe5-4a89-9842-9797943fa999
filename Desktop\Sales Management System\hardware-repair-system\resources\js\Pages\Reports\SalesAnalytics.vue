<script setup>
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import Chart from '@/Components/Chart.vue';
import { Head, Link } from '@inertiajs/vue3';
import { ref, computed } from 'vue';

const props = defineProps({
    salesData: {
        type: Object,
        default: () => ({
            total_revenue: 0,
            total_orders: 0,
            average_order_value: 0,
            payment_methods: {},
            invoices: []
        })
    },
    revenueBreakdown: {
        type: Object,
        default: () => ({
            labor_revenue: 0,
            parts_revenue: 0,
            service_categories: []
        })
    },
    customerAnalytics: {
        type: Object,
        default: () => ({
            new_customers: 0,
            returning_customers: 0,
            customer_ltv: []
        })
    },
    paymentAnalytics: {
        type: Object,
        default: () => ({
            payment_methods: [],
            payment_timing: {
                avg_days_to_pay: 0,
                same_day_payments: 0,
                late_payments: 0
            }
        })
    },
    filters: {
        type: Object,
        default: () => ({})
    },
    dateRange: {
        type: Object,
        default: () => ({
            start: new Date().toISOString(),
            end: new Date().toISOString()
        })
    },
});

const formatCurrency = (amount) => {
    return '₱' + parseFloat(amount || 0).toLocaleString('en-US', { minimumFractionDigits: 2 });
};

const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    });
};

// Calculate percentages for payment methods with null guards
const paymentMethodsWithPercentage = computed(() => {
    const total = props.salesData?.total_revenue || 0;
    const paymentMethods = props.salesData?.payment_methods || {};
    return Object.entries(paymentMethods).map(([method, data]) => ({
        method: method.replace('_', ' ').toUpperCase(),
        count: data?.count || 0,
        total: data?.total || 0,
        percentage: total > 0 ? ((data?.total || 0) / total * 100).toFixed(1) : 0
    }));
});

// Calculate service category percentages with null guards
const serviceCategoriesWithPercentage = computed(() => {
    const laborRevenue = props.revenueBreakdown?.labor_revenue || 0;
    const partsRevenue = props.revenueBreakdown?.parts_revenue || 0;
    const total = laborRevenue + partsRevenue;
    const categories = props.revenueBreakdown?.service_categories || [];
    return categories.map(category => ({
        ...category,
        percentage: total > 0 ? ((category?.revenue || 0) / total * 100).toFixed(1) : 0
    }));
});

// Payment methods chart data
const paymentMethodsChartData = computed(() => {
    const methods = paymentMethodsWithPercentage.value;
    return {
        labels: methods.map(m => m.method),
        datasets: [{
            data: methods.map(m => m.total),
            backgroundColor: [
                'rgba(59, 130, 246, 0.8)',   // blue
                'rgba(16, 185, 129, 0.8)',   // green
                'rgba(245, 158, 11, 0.8)',   // yellow
                'rgba(239, 68, 68, 0.8)',    // red
                'rgba(139, 92, 246, 0.8)',   // purple
            ],
            borderColor: [
                'rgb(59, 130, 246)',
                'rgb(16, 185, 129)',
                'rgb(245, 158, 11)',
                'rgb(239, 68, 68)',
                'rgb(139, 92, 246)',
            ],
            borderWidth: 2,
        }]
    };
});

// Revenue breakdown chart data
const revenueBreakdownChartData = computed(() => {
    return {
        labels: ['Labor Revenue', 'Parts Revenue'],
        datasets: [{
            data: [props.revenueBreakdown.labor_revenue, props.revenueBreakdown.parts_revenue],
            backgroundColor: [
                'rgba(59, 130, 246, 0.8)',
                'rgba(16, 185, 129, 0.8)',
            ],
            borderColor: [
                'rgb(59, 130, 246)',
                'rgb(16, 185, 129)',
            ],
            borderWidth: 2,
        }]
    };
});
</script>

<template>
    <Head title="Sales Analytics" />

    <AuthenticatedLayout>
        <template #header>
            <div class="flex items-center justify-between">
                <div>
                    <h2 class="text-xl font-semibold leading-tight text-white">
                        Sales Analytics
                    </h2>
                    <p class="text-sm text-gray-400 mt-1">
                        {{ formatDate(dateRange?.start) }} - {{ formatDate(dateRange?.end) }}
                    </p>
                </div>
                <div class="flex space-x-3">
                    <Link :href="route('reports.index')" 
                          class="bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors duration-200">
                        Back to Dashboard
                    </Link>
                </div>
            </div>
        </template>

        <div class="py-12">
            <div class="max-w-7xl mx-auto sm:px-6 lg:px-8 space-y-8">
                
                <!-- Key Sales Metrics -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div class="bg-gradient-to-br from-green-900 to-green-800 border border-green-700 rounded-xl p-6 shadow-xl">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm text-green-200">Total Revenue</p>
                                <p class="text-3xl font-bold text-white">{{ formatCurrency(salesData?.total_revenue || 0) }}</p>
                            </div>
                            <div class="p-3 bg-green-800 rounded-lg">
                                <svg class="w-8 h-8 text-green-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                                </svg>
                            </div>
                        </div>
                    </div>

                    <div class="bg-gradient-to-br from-blue-900 to-blue-800 border border-blue-700 rounded-xl p-6 shadow-xl">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm text-blue-200">Total Orders</p>
                                <p class="text-3xl font-bold text-white">{{ (salesData?.total_orders || 0).toLocaleString() }}</p>
                            </div>
                            <div class="p-3 bg-blue-800 rounded-lg">
                                <svg class="w-8 h-8 text-blue-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                </svg>
                            </div>
                        </div>
                    </div>

                    <div class="bg-gradient-to-br from-purple-900 to-purple-800 border border-purple-700 rounded-xl p-6 shadow-xl">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm text-purple-200">Average Order Value</p>
                                <p class="text-3xl font-bold text-white">{{ formatCurrency(salesData?.average_order_value || 0) }}</p>
                            </div>
                            <div class="p-3 bg-purple-800 rounded-lg">
                                <svg class="w-8 h-8 text-purple-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                                </svg>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Revenue Breakdown with Charts -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <!-- Labor vs Parts Revenue Chart -->
                    <div class="bg-gray-800 rounded-xl p-6 border border-gray-700 shadow-xl">
                        <h3 class="text-lg font-semibold text-white mb-6">Revenue Breakdown</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <Chart
                                    type="doughnut"
                                    :data="revenueBreakdownChartData"
                                    :height="200"
                                />
                            </div>
                            <div class="space-y-4">
                                <div class="flex justify-between items-center p-3 bg-gray-700 rounded-lg">
                                    <div class="flex items-center">
                                        <div class="w-4 h-4 bg-blue-500 rounded mr-3"></div>
                                        <span class="text-white text-sm">Labor Revenue</span>
                                    </div>
                                    <span class="text-white font-semibold text-sm">{{ formatCurrency(revenueBreakdown?.labor_revenue || 0) }}</span>
                                </div>
                                <div class="flex justify-between items-center p-3 bg-gray-700 rounded-lg">
                                    <div class="flex items-center">
                                        <div class="w-4 h-4 bg-green-500 rounded mr-3"></div>
                                        <span class="text-white text-sm">Parts Revenue</span>
                                    </div>
                                    <span class="text-white font-semibold text-sm">{{ formatCurrency(revenueBreakdown?.parts_revenue || 0) }}</span>
                                </div>
                                <div class="pt-3 border-t border-gray-600">
                                    <div class="flex justify-between items-center">
                                        <span class="text-gray-400 text-sm">Total Revenue</span>
                                        <span class="text-white font-bold">
                                            {{ formatCurrency((revenueBreakdown?.labor_revenue || 0) + (revenueBreakdown?.parts_revenue || 0)) }}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Payment Methods Chart -->
                    <div class="bg-gray-800 rounded-xl p-6 border border-gray-700 shadow-xl">
                        <h3 class="text-lg font-semibold text-white mb-6">Payment Methods Distribution</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <Chart
                                    type="pie"
                                    :data="paymentMethodsChartData"
                                    :height="200"
                                />
                            </div>
                            <div class="space-y-3">
                                <div v-for="payment in paymentMethodsWithPercentage" :key="payment.method"
                                     class="flex justify-between items-center p-2 bg-gray-700 rounded-lg">
                                    <div>
                                        <p class="text-white font-medium text-sm">{{ payment.method }}</p>
                                        <p class="text-gray-400 text-xs">{{ payment.count }} transactions</p>
                                    </div>
                                    <div class="text-right">
                                        <p class="text-white font-semibold text-sm">{{ formatCurrency(payment.total) }}</p>
                                        <p class="text-gray-400 text-xs">{{ payment.percentage }}%</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Service Categories Performance -->
                <div class="bg-gray-800 rounded-xl p-6 border border-gray-700 shadow-xl">
                    <h3 class="text-lg font-semibold text-white mb-6">Service Categories Performance</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        <div v-for="category in serviceCategoriesWithPercentage" :key="category.category" 
                             class="bg-gray-700 rounded-lg p-4">
                            <div class="flex justify-between items-start mb-2">
                                <h4 class="text-white font-medium">{{ category.category }}</h4>
                                <span class="text-sm text-gray-400">{{ category.percentage }}%</span>
                            </div>
                            <p class="text-2xl font-bold text-green-400">{{ formatCurrency(category.revenue) }}</p>
                            <div class="mt-2 bg-gray-600 rounded-full h-2">
                                <div class="bg-green-500 h-2 rounded-full" 
                                     :style="{ width: category.percentage + '%' }"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Customer Analytics -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <!-- Customer Acquisition -->
                    <div class="bg-gray-800 rounded-xl p-6 border border-gray-700 shadow-xl">
                        <h3 class="text-lg font-semibold text-white mb-6">Customer Analytics</h3>
                        <div class="space-y-4">
                            <div class="flex justify-between items-center p-4 bg-gray-700 rounded-lg">
                                <div>
                                    <p class="text-white font-medium">New Customers</p>
                                    <p class="text-gray-400 text-sm">First-time customers</p>
                                </div>
                                <span class="text-2xl font-bold text-blue-400">{{ customerAnalytics?.new_customers || 0 }}</span>
                            </div>
                            <div class="flex justify-between items-center p-4 bg-gray-700 rounded-lg">
                                <div>
                                    <p class="text-white font-medium">Returning Customers</p>
                                    <p class="text-gray-400 text-sm">Repeat customers</p>
                                </div>
                                <span class="text-2xl font-bold text-green-400">{{ customerAnalytics?.returning_customers || 0 }}</span>
                            </div>
                            <div class="pt-4 border-t border-gray-600">
                                <div class="flex justify-between items-center">
                                    <span class="text-gray-400">Customer Retention Rate</span>
                                    <span class="text-white font-bold">
                                        {{ (((customerAnalytics?.returning_customers || 0) / ((customerAnalytics?.new_customers || 0) + (customerAnalytics?.returning_customers || 0)) || 1) * 100).toFixed(1) }}%
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Top Customers -->
                    <div class="bg-gray-800 rounded-xl p-6 border border-gray-700 shadow-xl">
                        <h3 class="text-lg font-semibold text-white mb-6">Top Customers by Value</h3>
                        <div class="space-y-3">
                            <div v-for="(customer, index) in (customerAnalytics?.customer_ltv || []).slice(0, 5)" :key="customer?.id || index"
                                 class="flex items-center justify-between p-3 bg-gray-700 rounded-lg">
                                <div class="flex items-center">
                                    <div class="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-bold text-sm mr-3">
                                        {{ index + 1 }}
                                    </div>
                                    <div>
                                        <p class="text-white font-medium">{{ customer?.first_name || 'N/A' }} {{ customer?.last_name || '' }}</p>
                                        <p class="text-gray-400 text-sm">{{ customer?.total_orders || 0 }} orders</p>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <p class="text-white font-semibold">{{ formatCurrency(customer?.lifetime_value || 0) }}</p>
                                    <p class="text-gray-400 text-sm">{{ formatCurrency(customer?.average_order_value || 0) }} avg</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Payment Analytics -->
                <div class="bg-gray-800 rounded-xl p-6 border border-gray-700 shadow-xl">
                    <h3 class="text-lg font-semibold text-white mb-6">Payment Analytics</h3>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div class="text-center">
                            <p class="text-gray-400 text-sm">Average Days to Pay</p>
                            <p class="text-3xl font-bold text-white">{{ Math.round(paymentAnalytics.payment_timing?.avg_days_to_pay || 0) }}</p>
                            <p class="text-gray-400 text-xs">days</p>
                        </div>
                        <div class="text-center">
                            <p class="text-gray-400 text-sm">Same Day Payments</p>
                            <p class="text-3xl font-bold text-green-400">{{ paymentAnalytics.payment_timing?.same_day_payments || 0 }}</p>
                            <p class="text-gray-400 text-xs">invoices</p>
                        </div>
                        <div class="text-center">
                            <p class="text-gray-400 text-sm">Late Payments</p>
                            <p class="text-3xl font-bold text-red-400">{{ paymentAnalytics.payment_timing?.late_payments || 0 }}</p>
                            <p class="text-gray-400 text-xs">invoices</p>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </AuthenticatedLayout>
</template>

<style scoped>
/* Custom styles for sales analytics */
</style>
