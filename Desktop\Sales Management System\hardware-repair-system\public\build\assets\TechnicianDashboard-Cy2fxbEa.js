import{_ as C}from"./TechnicianLayout-BxKVOHiU.js";import{g as i,o as n,a as c,d as g,h as j,w as d,b as e,t as o,F as h,y as p,l as m,f as x,n as y,i as l,c as V,q as v}from"./app-wnQ52fJE.js";import"./ApplicationLogo-BIrQV_G-.js";import"./DropdownLink-BLptVuux.js";const A={class:"flex items-center justify-between"},B={class:"flex items-center space-x-4"},M={class:"w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center shadow-lg"},O={class:"text-lg font-bold text-white"},S={class:"text-2xl font-bold text-white"},D={key:0,class:"text-sm text-blue-300 ml-2"},z={class:"text-gray-400 text-sm"},N={class:"flex items-center space-x-4"},T={class:"text-right"},H={class:"text-lg font-semibold text-white"},L={class:"p-6 space-y-8"},P={class:"grid grid-cols-1 md:grid-cols-4 gap-6"},$={class:"bg-gradient-to-br from-gray-900 to-gray-800 border border-gray-700 rounded-xl p-6 shadow-xl"},W={class:"flex items-center justify-between"},E={class:"text-3xl font-bold text-white"},F={class:"bg-gradient-to-br from-gray-900 to-gray-800 border border-gray-700 rounded-xl p-6 shadow-xl"},U={class:"flex items-center justify-between"},q={class:"text-3xl font-bold text-white"},I={class:"bg-gradient-to-br from-gray-900 to-gray-800 border border-gray-700 rounded-xl p-6 shadow-xl"},R={class:"flex items-center justify-between"},G={class:"text-3xl font-bold text-white"},J={class:"bg-gradient-to-br from-gray-900 to-gray-800 border border-gray-700 rounded-xl p-6 shadow-xl"},K={class:"flex items-center justify-between"},Q={class:"text-3xl font-bold text-white"},X={class:"grid grid-cols-1 lg:grid-cols-3 gap-8"},Y={class:"lg:col-span-2"},Z={class:"bg-gradient-to-br from-gray-900 to-gray-800 border border-gray-700 rounded-xl shadow-xl"},ee={class:"px-6 py-4 border-b border-gray-700 bg-gradient-to-r from-gray-800 to-gray-700 rounded-t-xl"},te={class:"flex items-center justify-between"},se={class:"text-sm text-gray-400"},oe={class:"p-6"},re={key:0,class:"space-y-4"},ne={class:"flex items-start justify-between mb-3"},ae={class:"flex-1"},ie={class:"flex items-center space-x-3 mb-2"},de={class:"text-sm text-gray-300 mb-1"},le={class:"text-sm text-gray-400"},ce={class:"text-xs text-gray-500 mt-2"},ge={class:"flex flex-col items-end space-y-2"},xe={class:"flex space-x-1"},ue=["onClick"],he=["onClick"],me=["onClick"],be={class:"text-xs text-gray-500"},pe={key:1,class:"text-center py-8"},ye={class:"space-y-6"},ve={class:"bg-gradient-to-br from-gray-900 to-gray-800 border border-gray-700 rounded-xl p-6 shadow-xl"},fe={class:"space-y-4"},we={class:"flex items-center justify-between"},_e={class:"text-xl font-bold text-white"},ke={class:"flex items-center justify-between"},Ce={class:"text-xl font-bold text-white"},je={class:"bg-gradient-to-br from-gray-900 to-gray-800 border border-gray-700 rounded-xl shadow-xl"},Ve={class:"p-6"},Ae={key:0,class:"space-y-3"},Be={class:"text-xs text-gray-400 mt-1"},Me={class:"text-xs text-gray-500"},Oe={key:1,class:"text-center py-4"},Te={__name:"TechnicianDashboard",props:{stats:Object,assignedOrders:Array,recentCompleted:Array,performanceStats:Object,technician:Object,isAdminView:{type:Boolean,default:!1}},setup(r){const u=(a,t)=>{v.patch(route("repair-orders.update-status",a),{status:t},{preserveScroll:!0,onSuccess:()=>{v.reload({only:["assignedOrders","stats","recentCompleted"]})}})},f=a=>{const t={pending:"bg-yellow-100 text-yellow-800 border-yellow-200",in_progress:"bg-blue-100 text-blue-800 border-blue-200",waiting_parts:"bg-orange-100 text-orange-800 border-orange-200",completed:"bg-green-100 text-green-800 border-green-200",cancelled:"bg-red-100 text-red-800 border-red-200",delivered:"bg-purple-100 text-purple-800 border-purple-200"};return t[a]||t.pending},w=a=>{const t={low:"bg-green-100 text-green-800 border-green-200",medium:"bg-yellow-100 text-yellow-800 border-yellow-200",high:"bg-orange-100 text-orange-800 border-orange-200",urgent:"bg-red-100 text-red-800 border-red-200"};return t[a]||t.medium},_=a=>new Date(a).toLocaleDateString(),k=a=>new Date(a).toLocaleString();return(a,t)=>(n(),i(h,null,[c(g(j),{title:"Technician Dashboard"}),c(C,{isAdminView:r.isAdminView},{header:d(()=>[e("div",A,[e("div",B,[e("div",M,[e("span",O,o(r.technician.user?.name?.charAt(0)),1)]),e("div",null,[e("h2",S,[x(" Welcome back, "+o(r.technician.user?.name)+" ",1),r.isAdminView?(n(),i("span",D,"(Admin View)")):l("",!0)]),e("p",z,o(r.technician.specialization)+" • Employee ID: "+o(r.technician.employee_id),1)])]),e("div",N,[r.isAdminView?(n(),V(g(m),{key:0,href:a.route("dashboard"),class:"bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center space-x-2"},{default:d(()=>t[0]||(t[0]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M10 19l-7-7m0 0l7-7m-7 7h18"})],-1),x(" Back to Admin ",-1)])),_:1,__:[0]},8,["href"])):l("",!0),e("div",T,[t[1]||(t[1]=e("p",{class:"text-sm text-gray-400"},"Today",-1)),e("p",H,o(new Date().toLocaleDateString()),1)])])])]),default:d(()=>[e("div",L,[e("div",P,[e("div",$,[e("div",W,[e("div",null,[t[2]||(t[2]=e("p",{class:"text-sm text-gray-400"},"Active Orders",-1)),e("p",E,o(r.stats.assigned_orders),1)]),t[3]||(t[3]=e("div",{class:"p-3 bg-blue-600 rounded-lg"},[e("svg",{class:"w-6 h-6 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})])],-1))])]),e("div",F,[e("div",U,[e("div",null,[t[4]||(t[4]=e("p",{class:"text-sm text-gray-400"},"Completed Today",-1)),e("p",q,o(r.stats.completed_today),1)]),t[5]||(t[5]=e("div",{class:"p-3 bg-green-600 rounded-lg"},[e("svg",{class:"w-6 h-6 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1))])]),e("div",I,[e("div",R,[e("div",null,[t[6]||(t[6]=e("p",{class:"text-sm text-gray-400"},"Total Completed",-1)),e("p",G,o(r.stats.total_completed),1)]),t[7]||(t[7]=e("div",{class:"p-3 bg-purple-600 rounded-lg"},[e("svg",{class:"w-6 h-6 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"})])],-1))])]),e("div",J,[e("div",K,[e("div",null,[t[8]||(t[8]=e("p",{class:"text-sm text-gray-400"},"Pending Orders",-1)),e("p",Q,o(r.stats.pending_orders),1)]),t[9]||(t[9]=e("div",{class:"p-3 bg-yellow-600 rounded-lg"},[e("svg",{class:"w-6 h-6 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1))])])]),e("div",X,[e("div",Y,[e("div",Z,[e("div",ee,[e("div",te,[t[10]||(t[10]=e("div",{class:"flex items-center space-x-3"},[e("div",{class:"p-2 bg-blue-600 rounded-lg"},[e("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"})])]),e("h3",{class:"text-lg font-semibold text-white"},"My Assigned Orders")],-1)),e("span",se,o(r.assignedOrders.length)+" active",1)])]),e("div",oe,[r.assignedOrders.length>0?(n(),i("div",re,[(n(!0),i(h,null,p(r.assignedOrders,s=>(n(),i("div",{key:s.id,class:"bg-gray-800 rounded-lg p-4 border border-gray-700 hover:border-gray-600 transition-colors duration-200"},[e("div",ne,[e("div",ae,[e("div",ie,[c(g(m),{href:a.route("repair-orders.show",s.id),class:"text-lg font-semibold text-white hover:text-red-300 transition-colors duration-200"},{default:d(()=>[x(o(s.order_number),1)]),_:2},1032,["href"]),e("span",{class:y(["inline-flex items-center px-2 py-1 text-xs font-medium rounded-full border",w(s.priority)])},o(s.priority.toUpperCase()),3)]),e("p",de,o(s.customer?.full_name),1),e("p",le,o(s.device?.brand)+" "+o(s.device?.model),1),e("p",ce,o(s.service?.name),1)]),e("div",ge,[e("span",{class:y(["inline-flex items-center px-2 py-1 text-xs font-medium rounded-full border",f(s.status)])},o(s.status.replace("_"," ").toUpperCase()),3),e("div",xe,[s.status==="pending"?(n(),i("button",{key:0,onClick:b=>u(s.id,"in_progress"),class:"px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white text-xs rounded-lg transition-colors duration-200"}," Start Work ",8,ue)):l("",!0),s.status==="in_progress"?(n(),i("button",{key:1,onClick:b=>u(s.id,"completed"),class:"px-3 py-1 bg-green-600 hover:bg-green-700 text-white text-xs rounded-lg transition-colors duration-200"}," Complete ",8,he)):l("",!0),s.status==="in_progress"?(n(),i("button",{key:2,onClick:b=>u(s.id,"waiting_parts"),class:"px-3 py-1 bg-orange-600 hover:bg-orange-700 text-white text-xs rounded-lg transition-colors duration-200"}," Wait Parts ",8,me)):l("",!0)])])]),e("div",be," Created: "+o(k(s.created_at)),1)]))),128))])):(n(),i("div",pe,t[11]||(t[11]=[e("svg",{class:"w-12 h-12 text-gray-600 mx-auto mb-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"1",d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})],-1),e("p",{class:"text-gray-400 text-sm"},"No active orders assigned",-1)])))])])]),e("div",ye,[e("div",ve,[t[14]||(t[14]=e("div",{class:"flex items-center space-x-3 mb-4"},[e("div",{class:"p-2 bg-purple-600 rounded-lg"},[e("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"})])]),e("h3",{class:"text-lg font-semibold text-white"},"Performance (30 Days)")],-1)),e("div",fe,[e("div",we,[t[12]||(t[12]=e("span",{class:"text-gray-400"},"Orders Completed",-1)),e("span",_e,o(r.performanceStats.orders_completed),1)]),e("div",ke,[t[13]||(t[13]=e("span",{class:"text-gray-400"},"Avg. Completion",-1)),e("span",Ce,o(r.performanceStats.avg_completion_time?Math.round(r.performanceStats.avg_completion_time)+" days":"N/A"),1)])])]),e("div",je,[t[16]||(t[16]=e("div",{class:"px-6 py-4 border-b border-gray-700 bg-gradient-to-r from-gray-800 to-gray-700 rounded-t-xl"},[e("div",{class:"flex items-center space-x-3"},[e("div",{class:"p-2 bg-green-600 rounded-lg"},[e("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})])]),e("h3",{class:"text-lg font-semibold text-white"},"Recently Completed")])],-1)),e("div",Ve,[r.recentCompleted.length>0?(n(),i("div",Ae,[(n(!0),i(h,null,p(r.recentCompleted,s=>(n(),i("div",{key:s.id,class:"bg-gray-800 rounded-lg p-3 border border-gray-700"},[c(g(m),{href:a.route("repair-orders.show",s.id),class:"text-sm font-medium text-white hover:text-red-300 transition-colors duration-200"},{default:d(()=>[x(o(s.order_number),1)]),_:2},1032,["href"]),e("p",Be,o(s.customer?.full_name),1),e("p",Me,o(_(s.actual_completion)),1)]))),128))])):(n(),i("div",Oe,t[15]||(t[15]=[e("p",{class:"text-gray-400 text-sm"},"No completed orders yet",-1)])))])])])])])]),_:1},8,["isAdminView"])],64))}};export{Te as default};
