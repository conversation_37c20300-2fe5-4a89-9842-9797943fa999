import{_ as S}from"./AuthenticatedLayout-D449e8ZD.js";import{_ as A,m as y,g as a,o as n,a as u,d as _,h as k,w as m,b as t,t as o,F as c,y as x,n as b,J as p,l as D,f as j}from"./app-wnQ52fJE.js";import"./ApplicationLogo-BIrQV_G-.js";import"./DropdownLink-BLptVuux.js";const M={class:"flex items-center justify-between"},P={class:"text-sm text-gray-400 mt-1"},V={class:"flex space-x-3"},B={class:"py-12"},N={class:"max-w-7xl mx-auto sm:px-6 lg:px-8 space-y-8"},C={class:"grid grid-cols-1 md:grid-cols-3 gap-6"},O={class:"bg-gradient-to-br from-green-900 to-green-800 border border-green-700 rounded-xl p-6 shadow-xl"},R={class:"flex items-center justify-between"},$={class:"text-3xl font-bold text-white"},E={class:"bg-gradient-to-br from-blue-900 to-blue-800 border border-blue-700 rounded-xl p-6 shadow-xl"},F={class:"flex items-center justify-between"},z={class:"text-3xl font-bold text-white"},T={class:"bg-gradient-to-br from-purple-900 to-purple-800 border border-purple-700 rounded-xl p-6 shadow-xl"},H={class:"flex items-center justify-between"},L={class:"text-3xl font-bold text-white"},I={class:"bg-gray-800 rounded-xl p-6 border border-gray-700 shadow-xl"},U={class:"overflow-x-auto"},J={class:"w-full text-sm"},q={class:"py-3"},G={class:"flex items-center"},K={class:"w-6 h-6 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-bold text-xs mr-3"},Q={class:"text-white font-medium"},W={class:"py-3 text-right text-white"},X={class:"py-3 text-right text-green-400 font-semibold"},Y={class:"py-3 text-right text-white"},Z={class:"py-3 text-right text-gray-400"},tt={class:"py-3 text-right text-gray-400"},et={class:"grid grid-cols-1 lg:grid-cols-2 gap-6"},st={class:"bg-gray-800 rounded-xl p-6 border border-gray-700 shadow-xl"},ot={class:"space-y-4"},rt={class:"flex justify-between items-center mb-2"},lt={class:"text-white font-medium"},it={class:"grid grid-cols-2 gap-4 text-sm"},at={class:"text-white"},nt={class:"text-white"},dt={class:"mt-2 bg-gray-600 rounded-full h-2"},ct={class:"bg-gray-800 rounded-xl p-6 border border-gray-700 shadow-xl"},xt={class:"space-y-4"},gt={class:"flex justify-between items-center mb-2"},ut={class:"text-white font-medium"},mt={class:"text-green-400 font-semibold"},ht={class:"grid grid-cols-3 gap-2 text-sm"},ft={class:"text-white"},yt={class:"text-red-400"},_t={class:"text-green-400"},bt={class:"mt-2 bg-gray-600 rounded-full h-2"},pt={class:"bg-gray-800 rounded-xl p-6 border border-gray-700 shadow-xl"},vt={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4"},wt={class:"flex justify-between items-start mb-2"},St={class:"text-white font-medium text-sm"},At={class:"text-xs text-gray-400"},kt={class:"text-2xl font-bold text-blue-400"},Dt={class:"grid grid-cols-1 md:grid-cols-4 gap-6"},jt={class:"bg-gray-800 rounded-xl p-6 border border-gray-700 shadow-xl text-center"},Mt={class:"text-xl font-bold text-blue-400"},Pt={class:"text-gray-400 text-sm mt-2"},Vt={class:"bg-gray-800 rounded-xl p-6 border border-gray-700 shadow-xl text-center"},Bt={class:"text-xl font-bold text-green-400"},Nt={class:"text-gray-400 text-sm mt-2"},Ct={class:"bg-gray-800 rounded-xl p-6 border border-gray-700 shadow-xl text-center"},Ot={class:"text-xl font-bold text-purple-400"},Rt={class:"text-gray-400 text-sm mt-2"},$t={class:"bg-gray-800 rounded-xl p-6 border border-gray-700 shadow-xl text-center"},Et={class:"text-xl font-bold text-yellow-400"},Ft={class:"text-gray-400 text-sm mt-2"},zt={__name:"ServiceAnalytics",props:{serviceData:{type:Object,default:()=>({service_data:[],total_service_revenue:0,total_service_orders:0})},performanceMetrics:{type:Array,default:()=>[]},demandAnalysis:{type:Array,default:()=>[]},profitabilityAnalysis:{type:Array,default:()=>[]},filters:{type:Object,default:()=>({})},dateRange:{type:Object,default:()=>({start:new Date().toISOString(),end:new Date().toISOString()})}},setup(r){const h=r,l=i=>"₱"+parseFloat(i||0).toLocaleString("en-US",{minimumFractionDigits:2}),f=i=>new Date(i).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"}),v=i=>{const e=Math.floor(i/60),s=i%60;return e>0?`${e}h ${s}m`:`${s}m`},d=y(()=>h.serviceData?.service_data?.slice(0,10)||[]),g=y(()=>(h.performanceMetrics||[]).map(e=>({...e,efficiency:(e?.estimated_hours||0)>0?((e?.estimated_hours||0)/(e?.avg_completion_hours||1)*100).toFixed(1):0,variance:(e?.estimated_hours||0)>0?((e?.avg_completion_hours||0)-(e?.estimated_hours||0)).toFixed(1):0})));return(i,e)=>(n(),a(c,null,[u(_(k),{title:"Service Analytics"}),u(S,null,{header:m(()=>[t("div",M,[t("div",null,[e[0]||(e[0]=t("h2",{class:"text-xl font-semibold leading-tight text-white"}," Service Performance Analytics ",-1)),t("p",P,o(f(r.dateRange.start))+" - "+o(f(r.dateRange.end)),1)]),t("div",V,[u(_(D),{href:i.route("reports.index"),class:"bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors duration-200"},{default:m(()=>e[1]||(e[1]=[j(" Back to Dashboard ",-1)])),_:1,__:[1]},8,["href"])])])]),default:m(()=>[t("div",B,[t("div",N,[t("div",C,[t("div",O,[t("div",R,[t("div",null,[e[2]||(e[2]=t("p",{class:"text-sm text-green-200"},"Total Service Revenue",-1)),t("p",$,o(l(r.serviceData.total_service_revenue)),1)]),e[3]||(e[3]=t("div",{class:"p-3 bg-green-800 rounded-lg"},[t("svg",{class:"w-8 h-8 text-green-200",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})])],-1))])]),t("div",E,[t("div",F,[t("div",null,[e[4]||(e[4]=t("p",{class:"text-sm text-blue-200"},"Total Service Orders",-1)),t("p",z,o(r.serviceData.total_service_orders?.toLocaleString()||0),1)]),e[5]||(e[5]=t("div",{class:"p-3 bg-blue-800 rounded-lg"},[t("svg",{class:"w-8 h-8 text-blue-200",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})])],-1))])]),t("div",T,[t("div",H,[t("div",null,[e[6]||(e[6]=t("p",{class:"text-sm text-purple-200"},"Average Service Value",-1)),t("p",L,o(r.serviceData.total_service_orders>0?l(r.serviceData.total_service_revenue/r.serviceData.total_service_orders):l(0)),1)]),e[7]||(e[7]=t("div",{class:"p-3 bg-purple-800 rounded-lg"},[t("svg",{class:"w-8 h-8 text-purple-200",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"})])],-1))])])]),t("div",I,[e[9]||(e[9]=t("h3",{class:"text-lg font-semibold text-white mb-6"},"Top Performing Services",-1)),t("div",U,[t("table",J,[e[8]||(e[8]=t("thead",null,[t("tr",{class:"border-b border-gray-700"},[t("th",{class:"text-left text-gray-400 font-medium py-3"},"Service"),t("th",{class:"text-right text-gray-400 font-medium py-3"},"Orders"),t("th",{class:"text-right text-gray-400 font-medium py-3"},"Revenue"),t("th",{class:"text-right text-gray-400 font-medium py-3"},"Avg Price"),t("th",{class:"text-right text-gray-400 font-medium py-3"},"Base Price"),t("th",{class:"text-right text-gray-400 font-medium py-3"},"Est. Duration")])],-1)),t("tbody",null,[(n(!0),a(c,null,x(d.value,(s,w)=>(n(),a("tr",{key:s.name,class:"border-b border-gray-700 hover:bg-gray-700/50"},[t("td",q,[t("div",G,[t("div",K,o(w+1),1),t("span",Q,o(s.name),1)])]),t("td",W,o(s.total_orders),1),t("td",X,o(l(s.total_revenue)),1),t("td",Y,o(l(s.avg_price)),1),t("td",Z,o(l(s.base_price)),1),t("td",tt,o(v(s.estimated_duration)),1)]))),128))])])])]),t("div",et,[t("div",st,[e[12]||(e[12]=t("h3",{class:"text-lg font-semibold text-white mb-6"},"Service Efficiency Analysis",-1)),t("div",ot,[(n(!0),a(c,null,x(g.value.slice(0,8),s=>(n(),a("div",{key:s.name,class:"p-4 bg-gray-700 rounded-lg"},[t("div",rt,[t("h4",lt,o(s.name),1),t("span",{class:b([s.efficiency>=100?"text-green-400":s.efficiency>=80?"text-yellow-400":"text-red-400","font-semibold"])},o(s.efficiency)+"% efficient ",3)]),t("div",it,[t("div",null,[e[10]||(e[10]=t("p",{class:"text-gray-400"},"Estimated",-1)),t("p",at,o(s.estimated_hours)+"h",1)]),t("div",null,[e[11]||(e[11]=t("p",{class:"text-gray-400"},"Actual Avg",-1)),t("p",nt,o(s.avg_completion_hours)+"h",1)])]),t("div",dt,[t("div",{class:b([s.efficiency>=100?"bg-green-500":s.efficiency>=80?"bg-yellow-500":"bg-red-500","h-2 rounded-full"]),style:p({width:Math.min(100,s.efficiency)+"%"})},null,6)])]))),128))])]),t("div",ct,[e[16]||(e[16]=t("h3",{class:"text-lg font-semibold text-white mb-6"},"Service Profitability",-1)),t("div",xt,[(n(!0),a(c,null,x(r.profitabilityAnalysis.slice(0,8),s=>(n(),a("div",{key:s.name,class:"p-4 bg-gray-700 rounded-lg"},[t("div",gt,[t("h4",ut,o(s.name),1),t("span",mt,o(l(s.net_profit)),1)]),t("div",ht,[t("div",null,[e[13]||(e[13]=t("p",{class:"text-gray-400"},"Revenue",-1)),t("p",ft,o(l(s.service_revenue)),1)]),t("div",null,[e[14]||(e[14]=t("p",{class:"text-gray-400"},"Parts Cost",-1)),t("p",yt,o(l(s.parts_cost)),1)]),t("div",null,[e[15]||(e[15]=t("p",{class:"text-gray-400"},"Net Profit",-1)),t("p",_t,o(l(s.net_profit)),1)])]),t("div",bt,[t("div",{class:"bg-green-500 h-2 rounded-full",style:p({width:s.service_revenue>0?Math.min(100,s.net_profit/s.service_revenue*100)+"%":"0%"})},null,4)])]))),128))])])]),t("div",pt,[e[18]||(e[18]=t("h3",{class:"text-lg font-semibold text-white mb-6"},"Service Demand Trends",-1)),t("div",vt,[(n(!0),a(c,null,x(r.demandAnalysis.slice(0,12),s=>(n(),a("div",{key:`${s.name}-${s.month}-${s.year}`,class:"bg-gray-700 rounded-lg p-4"},[t("div",wt,[t("h4",St,o(s.name),1),t("span",At,o(s.month)+"/"+o(s.year),1)]),t("p",kt,o(s.demand_count),1),e[17]||(e[17]=t("p",{class:"text-gray-400 text-sm"},"orders this month",-1))]))),128))])]),t("div",Dt,[t("div",jt,[e[19]||(e[19]=t("h4",{class:"text-lg font-semibold text-white mb-2"},"Most Popular Service",-1)),t("p",Mt,o(d.value[0]?.name||"N/A"),1),t("p",Pt,o(d.value[0]?.total_orders||0)+" orders ",1)]),t("div",Vt,[e[20]||(e[20]=t("h4",{class:"text-lg font-semibold text-white mb-2"},"Highest Revenue Service",-1)),t("p",Bt,o(d.value[0]?.name||"N/A"),1),t("p",Nt,o(l(d.value[0]?.total_revenue||0)),1)]),t("div",Ct,[e[21]||(e[21]=t("h4",{class:"text-lg font-semibold text-white mb-2"},"Most Efficient Service",-1)),t("p",Ot,o(g.value[0]?.name||"N/A"),1),t("p",Rt,o(g.value[0]?.efficiency||0)+"% efficiency ",1)]),t("div",$t,[e[22]||(e[22]=t("h4",{class:"text-lg font-semibold text-white mb-2"},"Most Profitable Service",-1)),t("p",Et,o(r.profitabilityAnalysis[0]?.name||"N/A"),1),t("p",Ft,o(l(r.profitabilityAnalysis[0]?.net_profit||0)),1)])])])])]),_:1})],64))}},Ut=A(zt,[["__scopeId","data-v-fefd12d0"]]);export{Ut as default};
