import{_}from"./AuthenticatedLayout-D449e8ZD.js";import{C as p}from"./Chart-D7BRt3Gp.js";import{m as g,g as n,o as d,a as l,d as x,h as C,w as u,b as t,t as o,n as k,F as b,y as D,l as j}from"./app-wnQ52fJE.js";import"./ApplicationLogo-BIrQV_G-.js";import"./DropdownLink-BLptVuux.js";import"./chart-C26Vmg0g.js";const A={class:"flex items-center justify-between"},B={class:"flex items-center space-x-3"},F={class:"py-6"},R={class:"max-w-full mx-auto px-4 sm:px-6 lg:px-8 space-y-6"},M={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"},z={class:"bg-gray-800 rounded-xl p-6 border border-gray-700 shadow-xl"},L={class:"flex items-center justify-between"},N={class:"text-2xl font-bold text-white"},O={class:"mt-4 flex items-center"},U={class:"bg-gray-800 rounded-xl p-6 border border-gray-700 shadow-xl"},G={class:"flex items-center justify-between"},S={class:"text-2xl font-bold text-white"},V={class:"bg-gray-800 rounded-xl p-6 border border-gray-700 shadow-xl"},I={class:"flex items-center justify-between"},T={class:"text-2xl font-bold text-white"},$={class:"bg-gray-800 rounded-xl p-6 border border-gray-700 shadow-xl"},H={class:"flex items-center justify-between"},W={class:"text-2xl font-bold text-white"},E={class:"grid grid-cols-1 lg:grid-cols-2 gap-6"},P={class:"bg-gray-800 rounded-xl p-6 border border-gray-700 shadow-xl"},Z={class:"bg-gray-800 rounded-xl p-6 border border-gray-700 shadow-xl"},q={class:"bg-gray-800 rounded-xl border border-gray-700 shadow-xl"},J={class:"overflow-x-auto"},K={class:"min-w-full divide-y divide-gray-700"},Q={class:"bg-gray-800 divide-y divide-gray-700"},X={class:"px-6 py-4 whitespace-nowrap"},Y={class:"flex items-center"},tt={class:"flex-shrink-0 h-10 w-10"},et={class:"h-10 w-10 rounded-full bg-gray-600 flex items-center justify-center"},st={class:"text-sm font-medium text-white"},ot={class:"ml-4"},rt={class:"text-sm font-medium text-white"},at={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-300"},lt={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-300"},it={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-300"},nt={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-300"},xt={__name:"CustomersDetails",props:{customersData:{type:Object,default:()=>({})},customerGrowth:{type:Array,default:()=>[]},topCustomers:{type:Array,default:()=>[]},customerRetention:{type:Number,default:0},filters:{type:Object,default:()=>({})},dateRange:{type:Object,default:()=>({})}},setup(r){const c=r,i=s=>"₱"+parseFloat(s||0).toLocaleString("en-US",{minimumFractionDigits:2}),f=s=>`${s>=0?"+":""}${s.toFixed(1)}%`,h=s=>s>=0?"text-green-400":"text-red-400",y=g(()=>{const s=c.customerGrowth||[];return{labels:s.map(e=>e.date||""),datasets:[{label:"New Customers",data:s.map(e=>parseInt(e.count||0)),borderColor:"rgb(16, 185, 129)",backgroundColor:"rgba(16, 185, 129, 0.1)",tension:.4,fill:!0}]}}),v=g(()=>{const s=c.topCustomers.slice(0,5)||[];return{labels:s.map(e=>e.customer?.name||"Unknown"),datasets:[{label:"Customer Revenue",data:s.map(e=>parseFloat(e.revenue||0)),backgroundColor:["rgba(239, 68, 68, 0.8)","rgba(59, 130, 246, 0.8)","rgba(16, 185, 129, 0.8)","rgba(245, 158, 11, 0.8)","rgba(139, 92, 246, 0.8)"],borderColor:["rgb(239, 68, 68)","rgb(59, 130, 246)","rgb(16, 185, 129)","rgb(245, 158, 11)","rgb(139, 92, 246)"],borderWidth:2}]}}),m={responsive:!0,maintainAspectRatio:!1,plugins:{legend:{display:!0,position:"top",labels:{color:"#D1D5DB",font:{family:"Inter, system-ui, sans-serif"}}},tooltip:{backgroundColor:"#1F2937",titleColor:"#F9FAFB",bodyColor:"#D1D5DB",borderColor:"#374151",borderWidth:1,cornerRadius:8,callbacks:{label:function(s){let e=s.dataset.label||"";return e&&(e+=": "),s.parsed.y!==null&&(s.dataset.label==="Customer Revenue"?e+="₱"+s.parsed.y.toLocaleString("en-US",{minimumFractionDigits:2}):e+=s.parsed.y),e}}}},scales:{x:{ticks:{color:"#9CA3AF",font:{family:"Inter, system-ui, sans-serif"}},grid:{color:"#374151",borderColor:"#4B5563"}},y:{beginAtZero:!0,ticks:{color:"#9CA3AF",font:{family:"Inter, system-ui, sans-serif"}},grid:{color:"#374151",borderColor:"#4B5563"}}}};return(s,e)=>(d(),n(b,null,[l(x(C),{title:"Customers Details"}),l(_,null,{header:u(()=>[t("div",A,[t("div",null,[t("div",B,[l(x(j),{href:s.route("reports.index"),class:"text-gray-400 hover:text-white"},{default:u(()=>e[0]||(e[0]=[t("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 19l-7-7 7-7"})],-1)])),_:1,__:[0]},8,["href"]),e[1]||(e[1]=t("h2",{class:"text-xl font-semibold leading-tight text-white"}," Customers Details ",-1))]),e[2]||(e[2]=t("p",{class:"text-sm text-gray-400 mt-1"}," Comprehensive customer analytics and insights ",-1))])])]),default:u(()=>[t("div",F,[t("div",R,[t("div",M,[t("div",z,[t("div",L,[t("div",null,[e[3]||(e[3]=t("p",{class:"text-sm font-medium text-gray-400"},"Total Customers",-1)),t("p",N,o(r.customersData.total_customers||0),1)]),e[4]||(e[4]=t("div",{class:"p-3 bg-blue-500/20 rounded-lg"},[t("svg",{class:"w-6 h-6 text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"})])],-1))]),t("div",O,[t("span",{class:k([h(r.customersData.customer_growth),"text-sm font-medium"])},o(f(r.customersData.customer_growth||0)),3),e[5]||(e[5]=t("span",{class:"text-gray-400 text-sm ml-2"},"vs previous period",-1))])]),t("div",U,[t("div",G,[t("div",null,[e[6]||(e[6]=t("p",{class:"text-sm font-medium text-gray-400"},"New Customers",-1)),t("p",S,o(r.customersData.new_customers||0),1)]),e[7]||(e[7]=t("div",{class:"p-3 bg-green-500/20 rounded-lg"},[t("svg",{class:"w-6 h-6 text-green-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"})])],-1))])]),t("div",V,[t("div",I,[t("div",null,[e[8]||(e[8]=t("p",{class:"text-sm font-medium text-gray-400"},"Retention Rate",-1)),t("p",T,o(Math.round(r.customerRetention))+"% ",1)]),e[9]||(e[9]=t("div",{class:"p-3 bg-purple-500/20 rounded-lg"},[t("svg",{class:"w-6 h-6 text-purple-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"})])],-1))])]),t("div",$,[t("div",H,[t("div",null,[e[10]||(e[10]=t("p",{class:"text-sm font-medium text-gray-400"},"Avg Lifetime Value",-1)),t("p",W,o(i(r.customersData.customer_lifetime_value||0)),1)]),e[11]||(e[11]=t("div",{class:"p-3 bg-yellow-500/20 rounded-lg"},[t("svg",{class:"w-6 h-6 text-yellow-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})])],-1))])])]),t("div",E,[t("div",P,[e[12]||(e[12]=t("div",{class:"flex items-center justify-between mb-6"},[t("h3",{class:"text-lg font-semibold text-white"},"Customer Growth")],-1)),l(p,{type:"line",data:y.value,options:m,height:300},null,8,["data"])]),t("div",Z,[e[13]||(e[13]=t("div",{class:"flex items-center justify-between mb-6"},[t("h3",{class:"text-lg font-semibold text-white"},"Top Customers by Revenue")],-1)),l(p,{type:"bar",data:v.value,options:m,height:300},null,8,["data"])])]),t("div",q,[e[15]||(e[15]=t("div",{class:"px-6 py-4 border-b border-gray-700"},[t("h3",{class:"text-lg font-semibold text-white"},"Top Customers")],-1)),t("div",J,[t("table",K,[e[14]||(e[14]=t("thead",{class:"bg-gray-700"},[t("tr",null,[t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"},"Customer"),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"},"Revenue"),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"},"Orders"),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"},"Avg Order Value"),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"},"Contact")])],-1)),t("tbody",Q,[(d(!0),n(b,null,D(r.topCustomers,(a,w)=>(d(),n("tr",{key:w,class:"hover:bg-gray-700"},[t("td",X,[t("div",Y,[t("div",tt,[t("div",et,[t("span",st,o((a.customer?.name||"U").charAt(0).toUpperCase()),1)])]),t("div",ot,[t("div",rt,o(a.customer?.name||"Unknown"),1)])])]),t("td",at,o(i(a.revenue)),1),t("td",lt,o(a.orders_count),1),t("td",it,o(i(a.revenue/(a.orders_count||1))),1),t("td",nt,o(a.customer?.phone||"N/A"),1)]))),128))])])])])])])]),_:1})],64))}};export{xt as default};
