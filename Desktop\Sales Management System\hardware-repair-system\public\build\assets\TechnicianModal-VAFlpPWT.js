import{u as v,r as k,x as _,g as i,i as d,o as l,b as e,e as x,t as a,j as c,v as u,d as s,I as C,F as z,y as V,k as T,f as U}from"./app-wnQ52fJE.js";const E={class:"flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0"},M={class:"flex items-center justify-between mb-6"},S={class:"flex items-center space-x-3"},q={class:"text-xl font-bold text-white"},P={class:"text-sm text-gray-400"},X={class:"bg-gray-800 rounded-lg p-4 border border-gray-700"},j={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},A={key:0,class:"mt-1 text-sm text-red-400"},D={key:0,class:"mt-1 text-sm text-red-400"},N={key:0,class:"mt-1 text-sm text-red-400"},B={key:0,class:"mt-1 text-sm text-red-400"},F={key:0,class:"bg-gray-800 rounded-lg p-4 border border-gray-700"},I={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},R={key:0,class:"mt-1 text-sm text-red-400"},L={key:0,class:"mt-1 text-sm text-red-400"},H={class:"bg-gray-800 rounded-lg p-4 border border-gray-700"},O={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},G=["value"],J={key:0,class:"mt-1 text-sm text-red-400"},K={key:0,class:"mt-1 text-sm text-red-400"},Q={class:"grid grid-cols-1 md:grid-cols-2 gap-6 mt-6"},W={key:0,class:"mt-1 text-sm text-red-400"},Y={key:0,class:"mt-1 text-sm text-red-400"},Z={class:"mt-6"},$={key:0,class:"mt-1 text-sm text-red-400"},ee={key:0,class:"mt-6"},te={class:"flex items-center"},oe={class:"flex items-center justify-end space-x-4 pt-6 border-t border-gray-700"},se=["disabled"],re={key:0,class:"flex items-center"},ie={key:1},ae={__name:"TechnicianModal",props:{show:{type:Boolean,default:!1},technician:{type:Object,default:null}},emits:["close","saved"],setup(b,{emit:w}){const n=b,f=w,t=v({name:"",email:"",password:"",password_confirmation:"",employee_id:"",phone:"",specialization:"",hire_date:"",certifications:"",skills:"",notes:"",is_active:!0}),m=k(!1);_(()=>n.show,p=>{p&&(n.technician?(m.value=!0,t.name=n.technician.user?.name||"",t.email=n.technician.user?.email||"",t.employee_id=n.technician.employee_id||"",t.phone=n.technician.phone||"",t.specialization=n.technician.specialization||"",t.hire_date=n.technician.hire_date?new Date(n.technician.hire_date).toISOString().split("T")[0]:"",t.certifications=n.technician.certifications||"",t.skills=n.technician.skills||"",t.notes=n.technician.notes||"",t.is_active=n.technician.is_active??!0,t.password="",t.password_confirmation=""):(m.value=!1,t.reset(),t.is_active=!0))});const y=()=>{m.value?t.put(route("technicians.update",n.technician.id),{onSuccess:()=>{window.toast&&window.toast.success("Technician updated successfully!"),f("saved"),g()},onError:p=>{console.error("Technician update failed:",p),window.toast&&window.toast.error("Failed to update technician. Please try again.")}}):t.post(route("technicians.store"),{onSuccess:()=>{window.toast&&window.toast.success("Technician created successfully!"),f("saved"),g()},onError:p=>{console.error("Technician creation failed:",p),window.toast&&window.toast.error("Failed to create technician. Please try again.")}})},g=()=>{t.reset(),t.clearErrors(),f("close")},h=["Mobile Device Repair","Computer Hardware","Laptop Repair","Gaming Console Repair","Tablet Repair","Printer Repair","Network Equipment","Audio Equipment","General Electronics","Data Recovery","Software Troubleshooting","Other"];return(p,o)=>b.show?(l(),i("div",{key:0,class:"fixed inset-0 z-50 overflow-y-auto",onClick:g},[e("div",E,[o[32]||(o[32]=e("div",{class:"fixed inset-0 transition-opacity bg-black bg-opacity-75 backdrop-blur-sm"},null,-1)),e("div",{class:"inline-block w-full max-w-4xl p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-gradient-to-br from-gray-900 to-gray-800 border border-gray-700 shadow-2xl rounded-2xl",onClick:o[12]||(o[12]=x(()=>{},["stop"]))},[e("div",M,[e("div",S,[o[13]||(o[13]=e("div",{class:"p-2 bg-blue-600 rounded-lg"},[e("svg",{class:"w-6 h-6 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})])],-1)),e("div",null,[e("h3",q,a(m.value?"Edit Technician":"Add New Technician"),1),e("p",P,a(m.value?"Update technician information":"Create a new technician profile"),1)])]),e("button",{onClick:g,class:"p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded-lg transition-colors duration-200"},o[14]||(o[14]=[e("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),e("form",{onSubmit:x(y,["prevent"]),class:"space-y-6"},[e("div",X,[o[19]||(o[19]=e("h4",{class:"text-lg font-semibold text-white mb-4"},"Personal Information",-1)),e("div",j,[e("div",null,[o[15]||(o[15]=e("label",{for:"name",class:"block text-sm font-medium text-gray-300 mb-2"}," Full Name * ",-1)),c(e("input",{id:"name","onUpdate:modelValue":o[0]||(o[0]=r=>s(t).name=r),type:"text",class:"w-full rounded-lg bg-gray-700 border-gray-600 text-white shadow-sm focus:border-red-500 focus:ring-red-500 transition-colors duration-200",placeholder:"John Doe",required:""},null,512),[[u,s(t).name]]),s(t).errors.name?(l(),i("div",A,a(s(t).errors.name),1)):d("",!0)]),e("div",null,[o[16]||(o[16]=e("label",{for:"email",class:"block text-sm font-medium text-gray-300 mb-2"}," Email Address * ",-1)),c(e("input",{id:"email","onUpdate:modelValue":o[1]||(o[1]=r=>s(t).email=r),type:"email",class:"w-full rounded-lg bg-gray-700 border-gray-600 text-white shadow-sm focus:border-red-500 focus:ring-red-500 transition-colors duration-200",placeholder:"<EMAIL>",required:""},null,512),[[u,s(t).email]]),s(t).errors.email?(l(),i("div",D,a(s(t).errors.email),1)):d("",!0)]),e("div",null,[o[17]||(o[17]=e("label",{for:"phone",class:"block text-sm font-medium text-gray-300 mb-2"}," Phone Number ",-1)),c(e("input",{id:"phone","onUpdate:modelValue":o[2]||(o[2]=r=>s(t).phone=r),type:"tel",class:"w-full rounded-lg bg-gray-700 border-gray-600 text-white shadow-sm focus:border-red-500 focus:ring-red-500 transition-colors duration-200",placeholder:"+63 9XX XXX XXXX"},null,512),[[u,s(t).phone]]),s(t).errors.phone?(l(),i("div",N,a(s(t).errors.phone),1)):d("",!0)]),e("div",null,[o[18]||(o[18]=e("label",{for:"employee_id",class:"block text-sm font-medium text-gray-300 mb-2"}," Employee ID * ",-1)),c(e("input",{id:"employee_id","onUpdate:modelValue":o[3]||(o[3]=r=>s(t).employee_id=r),type:"text",class:"w-full rounded-lg bg-gray-700 border-gray-600 text-white shadow-sm focus:border-red-500 focus:ring-red-500 transition-colors duration-200",placeholder:"EMP001",required:""},null,512),[[u,s(t).employee_id]]),s(t).errors.employee_id?(l(),i("div",B,a(s(t).errors.employee_id),1)):d("",!0)])])]),m.value?d("",!0):(l(),i("div",F,[o[22]||(o[22]=e("h4",{class:"text-lg font-semibold text-white mb-4"},"Account Information",-1)),e("div",I,[e("div",null,[o[20]||(o[20]=e("label",{for:"password",class:"block text-sm font-medium text-gray-300 mb-2"}," Password * ",-1)),c(e("input",{id:"password","onUpdate:modelValue":o[4]||(o[4]=r=>s(t).password=r),type:"password",class:"w-full rounded-lg bg-gray-700 border-gray-600 text-white shadow-sm focus:border-red-500 focus:ring-red-500 transition-colors duration-200",required:""},null,512),[[u,s(t).password]]),s(t).errors.password?(l(),i("div",R,a(s(t).errors.password),1)):d("",!0)]),e("div",null,[o[21]||(o[21]=e("label",{for:"password_confirmation",class:"block text-sm font-medium text-gray-300 mb-2"}," Confirm Password * ",-1)),c(e("input",{id:"password_confirmation","onUpdate:modelValue":o[5]||(o[5]=r=>s(t).password_confirmation=r),type:"password",class:"w-full rounded-lg bg-gray-700 border-gray-600 text-white shadow-sm focus:border-red-500 focus:ring-red-500 transition-colors duration-200",required:""},null,512),[[u,s(t).password_confirmation]]),s(t).errors.password_confirmation?(l(),i("div",L,a(s(t).errors.password_confirmation),1)):d("",!0)])])])),e("div",H,[o[30]||(o[30]=e("h4",{class:"text-lg font-semibold text-white mb-4"},"Professional Information",-1)),e("div",O,[e("div",null,[o[24]||(o[24]=e("label",{for:"specialization",class:"block text-sm font-medium text-gray-300 mb-2"}," Specialization * ",-1)),c(e("select",{id:"specialization","onUpdate:modelValue":o[6]||(o[6]=r=>s(t).specialization=r),class:"w-full rounded-lg bg-gray-700 border-gray-600 text-white shadow-sm focus:border-red-500 focus:ring-red-500 transition-colors duration-200",required:""},[o[23]||(o[23]=e("option",{value:"",class:"bg-gray-700 text-white"},"Select specialization...",-1)),(l(),i(z,null,V(h,r=>e("option",{key:r,value:r,class:"bg-gray-700 text-white"},a(r),9,G)),64))],512),[[C,s(t).specialization]]),s(t).errors.specialization?(l(),i("div",J,a(s(t).errors.specialization),1)):d("",!0)]),e("div",null,[o[25]||(o[25]=e("label",{for:"hire_date",class:"block text-sm font-medium text-gray-300 mb-2"}," Hire Date * ",-1)),c(e("input",{id:"hire_date","onUpdate:modelValue":o[7]||(o[7]=r=>s(t).hire_date=r),type:"date",class:"w-full rounded-lg bg-gray-700 border-gray-600 text-white shadow-sm focus:border-red-500 focus:ring-red-500 transition-colors duration-200",required:""},null,512),[[u,s(t).hire_date]]),s(t).errors.hire_date?(l(),i("div",K,a(s(t).errors.hire_date),1)):d("",!0)])]),e("div",Q,[e("div",null,[o[26]||(o[26]=e("label",{for:"certifications",class:"block text-sm font-medium text-gray-300 mb-2"}," Certifications ",-1)),c(e("textarea",{id:"certifications","onUpdate:modelValue":o[8]||(o[8]=r=>s(t).certifications=r),rows:"3",class:"w-full rounded-lg bg-gray-700 border-gray-600 text-white shadow-sm focus:border-red-500 focus:ring-red-500 transition-colors duration-200",placeholder:"List relevant certifications..."},null,512),[[u,s(t).certifications]]),s(t).errors.certifications?(l(),i("div",W,a(s(t).errors.certifications),1)):d("",!0)]),e("div",null,[o[27]||(o[27]=e("label",{for:"skills",class:"block text-sm font-medium text-gray-300 mb-2"}," Skills & Expertise ",-1)),c(e("textarea",{id:"skills","onUpdate:modelValue":o[9]||(o[9]=r=>s(t).skills=r),rows:"3",class:"w-full rounded-lg bg-gray-700 border-gray-600 text-white shadow-sm focus:border-red-500 focus:ring-red-500 transition-colors duration-200",placeholder:"List technical skills and expertise..."},null,512),[[u,s(t).skills]]),s(t).errors.skills?(l(),i("div",Y,a(s(t).errors.skills),1)):d("",!0)])]),e("div",Z,[o[28]||(o[28]=e("label",{for:"notes",class:"block text-sm font-medium text-gray-300 mb-2"}," Additional Notes ",-1)),c(e("textarea",{id:"notes","onUpdate:modelValue":o[10]||(o[10]=r=>s(t).notes=r),rows:"3",class:"w-full rounded-lg bg-gray-700 border-gray-600 text-white shadow-sm focus:border-red-500 focus:ring-red-500 transition-colors duration-200",placeholder:"Any additional notes about the technician..."},null,512),[[u,s(t).notes]]),s(t).errors.notes?(l(),i("div",$,a(s(t).errors.notes),1)):d("",!0)]),m.value?(l(),i("div",ee,[e("label",te,[c(e("input",{"onUpdate:modelValue":o[11]||(o[11]=r=>s(t).is_active=r),type:"checkbox",class:"rounded bg-gray-700 border-gray-600 text-red-600 shadow-sm focus:border-red-500 focus:ring-red-500"},null,512),[[T,s(t).is_active]]),o[29]||(o[29]=e("span",{class:"ml-2 text-sm text-gray-300"},"Active Technician",-1))])])):d("",!0)]),e("div",oe,[e("button",{type:"button",onClick:g,class:"px-6 py-2 border border-gray-600 rounded-lg text-gray-300 hover:bg-gray-700 transition-colors duration-200 font-medium"}," Cancel "),e("button",{type:"submit",disabled:s(t).processing,class:"px-6 py-2 bg-gradient-to-r from-red-600 to-red-700 text-white rounded-lg hover:from-red-700 hover:to-red-800 disabled:opacity-50 transition-all duration-200 shadow-lg hover:shadow-xl font-medium"},[s(t).processing?(l(),i("span",re,[o[31]||(o[31]=e("svg",{class:"animate-spin -ml-1 mr-2 h-4 w-4 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"},[e("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"}),e("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})],-1)),U(" "+a(m.value?"Updating...":"Creating..."),1)])):(l(),i("span",ie,a(m.value?"Update Technician":"Create Technician"),1))],8,se)])],32)])])])):d("",!0)}};export{ae as _};
