import{_ as j}from"./AuthenticatedLayout-D449e8ZD.js";import{C as v}from"./Chart-D7BRt3Gp.js";import{m as x,g as m,o as g,a as o,d as l,h as B,w as a,b as t,l as i,t as n,n as u,f as w,F}from"./app-wnQ52fJE.js";import"./ApplicationLogo-BIrQV_G-.js";import"./DropdownLink-BLptVuux.js";import"./chart-C26Vmg0g.js";const S={class:"py-8"},R={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},L={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8"},V={class:"flex items-center justify-between"},A={class:"text-3xl font-bold mt-1"},M={class:"flex items-center mt-3"},O=["d"],T={class:"flex items-center justify-between"},z={class:"text-3xl font-bold mt-1"},I={class:"flex items-center mt-3"},N=["d"],U={class:"flex items-center justify-between"},W={class:"text-3xl font-bold mt-1"},$={class:"flex items-center mt-3"},G=["d"],H={class:"flex items-center justify-between"},E={class:"text-3xl font-bold mt-1"},P={class:"flex items-center mt-3"},Y={class:"text-orange-100 text-sm"},Z={class:"grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6 mb-8"},q={class:"lg:col-span-2 bg-gray-800 rounded-xl p-6 border border-gray-700 shadow-lg"},J={class:"flex items-center justify-between mb-6"},K={class:"bg-gray-800 rounded-xl p-6 border border-gray-700 shadow-lg"},Q={class:"flex items-center justify-between mb-6"},X={class:"bg-gray-800 rounded-xl p-6 border border-gray-700 shadow-lg"},tt={class:"flex items-center justify-between mb-6"},nt={__name:"NewDashboard",props:{metrics:{type:Object,default:()=>({})},charts:{type:Object,default:()=>({})},trends:{type:Object,default:()=>({})}},setup(r){const h=r,y=s=>"₱"+parseFloat(s||0).toLocaleString("en-US",{minimumFractionDigits:2}),p=s=>`${s>=0?"+":""}${s.toFixed(1)}%`,d=s=>s>=0?"text-green-400":"text-red-400",b=s=>s>=0?"M13 7h8m0 0v8m0-8l-8 8-4-4-6 6":"M13 17h8m0 0V9m0 8l-8-8-4 4-6-6",k=x(()=>{const s=h.charts.daily_revenue||[];return{labels:s.map(e=>new Date(e.date).toLocaleDateString()),datasets:[{label:"Daily Revenue",data:s.map(e=>parseFloat(e.revenue||0)),borderColor:"rgb(59, 130, 246)",backgroundColor:"rgba(59, 130, 246, 0.1)",tension:.4,fill:!0}]}}),C=x(()=>{const s=h.charts.orders_by_status||[],e={pending:"rgba(245, 158, 11, 0.8)",in_progress:"rgba(59, 130, 246, 0.8)",waiting_parts:"rgba(249, 115, 22, 0.8)",completed:"rgba(16, 185, 129, 0.8)",delivered:"rgba(34, 197, 94, 0.8)",cancelled:"rgba(239, 68, 68, 0.8)"};return{labels:s.map(c=>c.status.replace("_"," ").toUpperCase()),datasets:[{data:s.map(c=>c.count),backgroundColor:s.map(c=>e[c.status]||"rgba(156, 163, 175, 0.8)"),borderWidth:2}]}}),D=x(()=>{const s=h.charts.top_services||[];return{labels:s.map(e=>e.name),datasets:[{label:"Revenue",data:s.map(e=>parseFloat(e.revenue||0)),backgroundColor:"rgba(139, 92, 246, 0.8)",borderColor:"rgb(139, 92, 246)",borderWidth:2}]}}),f={responsive:!0,maintainAspectRatio:!1,plugins:{legend:{display:!0,position:"top",labels:{color:"#D1D5DB",font:{family:"Inter, system-ui, sans-serif"}}},tooltip:{backgroundColor:"#1F2937",titleColor:"#F9FAFB",bodyColor:"#D1D5DB",borderColor:"#374151",borderWidth:1,cornerRadius:8,callbacks:{label:function(s){let e=s.dataset.label||"";return e&&(e+=": "),s.parsed.y!==null&&(s.dataset.label==="Revenue"||s.dataset.label==="Daily Revenue"?e+="₱"+s.parsed.y.toLocaleString("en-US",{minimumFractionDigits:2}):e+=s.parsed.y),e}}}},scales:{x:{ticks:{color:"#9CA3AF",font:{family:"Inter, system-ui, sans-serif"}},grid:{color:"#374151",borderColor:"#4B5563"}},y:{beginAtZero:!0,ticks:{color:"#9CA3AF",font:{family:"Inter, system-ui, sans-serif"}},grid:{color:"#374151",borderColor:"#4B5563"}}}},_={...f,scales:void 0};return(s,e)=>(g(),m(F,null,[o(l(B),{title:"Reports Dashboard"}),o(j,null,{header:a(()=>e[0]||(e[0]=[t("div",{class:"flex items-center justify-between"},[t("div",null,[t("h2",{class:"text-2xl font-bold leading-tight text-white"}," Reports Dashboard "),t("p",{class:"text-sm text-gray-400 mt-1"}," Comprehensive business analytics and insights ")]),t("div",{class:"flex items-center space-x-3"},[t("select",{class:"bg-gray-700 border-gray-600 text-white rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"},[t("option",{value:"30days"},"Last 30 Days"),t("option",{value:"7days"},"Last 7 Days"),t("option",{value:"90days"},"Last 90 Days"),t("option",{value:"thisyear"},"This Year")])])],-1)])),default:a(()=>[t("div",S,[t("div",R,[t("div",L,[o(l(i),{href:s.route("reports.revenue"),class:"group bg-gradient-to-br from-blue-600 to-blue-700 rounded-xl p-6 text-white shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200"},{default:a(()=>[t("div",V,[t("div",null,[e[2]||(e[2]=t("p",{class:"text-blue-100 text-sm font-medium"},"Total Revenue",-1)),t("p",A,n(y(r.metrics.total_revenue)),1),t("div",M,[(g(),m("svg",{class:u(["w-4 h-4 mr-1",d(r.metrics.revenue_growth)]),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:b(r.metrics.revenue_growth)},null,8,O)],2)),t("span",{class:u(["text-sm",d(r.metrics.revenue_growth)])},n(p(r.metrics.revenue_growth)),3),e[1]||(e[1]=t("span",{class:"text-blue-100 text-sm ml-1"},"vs last month",-1))])]),e[3]||(e[3]=t("div",{class:"p-3 bg-blue-500 bg-opacity-30 rounded-lg group-hover:bg-opacity-40 transition-all duration-200"},[t("svg",{class:"w-8 h-8 text-blue-100",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})])],-1))])]),_:1},8,["href"]),o(l(i),{href:s.route("reports.orders"),class:"group bg-gradient-to-br from-green-600 to-green-700 rounded-xl p-6 text-white shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200"},{default:a(()=>[t("div",T,[t("div",null,[e[5]||(e[5]=t("p",{class:"text-green-100 text-sm font-medium"},"Total Orders",-1)),t("p",z,n(r.metrics.total_orders?.toLocaleString()),1),t("div",I,[(g(),m("svg",{class:u(["w-4 h-4 mr-1",d(r.metrics.orders_growth)]),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:b(r.metrics.orders_growth)},null,8,N)],2)),t("span",{class:u(["text-sm",d(r.metrics.orders_growth)])},n(p(r.metrics.orders_growth)),3),e[4]||(e[4]=t("span",{class:"text-green-100 text-sm ml-1"},"vs last month",-1))])]),e[6]||(e[6]=t("div",{class:"p-3 bg-green-500 bg-opacity-30 rounded-lg group-hover:bg-opacity-40 transition-all duration-200"},[t("svg",{class:"w-8 h-8 text-green-100",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})])],-1))])]),_:1},8,["href"]),o(l(i),{href:s.route("reports.customers"),class:"group bg-gradient-to-br from-purple-600 to-purple-700 rounded-xl p-6 text-white shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200"},{default:a(()=>[t("div",U,[t("div",null,[e[8]||(e[8]=t("p",{class:"text-purple-100 text-sm font-medium"},"Total Customers",-1)),t("p",W,n(r.metrics.total_customers?.toLocaleString()),1),t("div",$,[(g(),m("svg",{class:u(["w-4 h-4 mr-1",d(r.metrics.customers_growth)]),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:b(r.metrics.customers_growth)},null,8,G)],2)),t("span",{class:u(["text-sm",d(r.metrics.customers_growth)])},n(p(r.metrics.customers_growth)),3),e[7]||(e[7]=t("span",{class:"text-purple-100 text-sm ml-1"},"vs last month",-1))])]),e[9]||(e[9]=t("div",{class:"p-3 bg-purple-500 bg-opacity-30 rounded-lg group-hover:bg-opacity-40 transition-all duration-200"},[t("svg",{class:"w-8 h-8 text-purple-100",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"})])],-1))])]),_:1},8,["href"]),o(l(i),{href:s.route("reports.services"),class:"group bg-gradient-to-br from-orange-600 to-orange-700 rounded-xl p-6 text-white shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200"},{default:a(()=>[t("div",H,[t("div",null,[e[10]||(e[10]=t("p",{class:"text-orange-100 text-sm font-medium"},"Active Repairs",-1)),t("p",E,n(r.metrics.active_repairs?.toLocaleString()),1),t("div",P,[t("span",Y,n(Math.round(r.metrics.completion_rate))+"% completion rate ",1)])]),e[11]||(e[11]=t("div",{class:"p-3 bg-orange-500 bg-opacity-30 rounded-lg group-hover:bg-opacity-40 transition-all duration-200"},[t("svg",{class:"w-8 h-8 text-orange-100",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"})])],-1))])]),_:1},8,["href"])]),t("div",Z,[t("div",q,[t("div",J,[e[13]||(e[13]=t("h3",{class:"text-lg font-semibold text-white"},"Revenue Trend (Last 30 Days)",-1)),o(l(i),{href:s.route("reports.revenue"),class:"text-blue-400 hover:text-blue-300 text-sm font-medium"},{default:a(()=>e[12]||(e[12]=[w(" View Details → ",-1)])),_:1,__:[12]},8,["href"])]),o(v,{type:"line",data:k.value,options:f,height:300},null,8,["data"])]),t("div",K,[t("div",Q,[e[15]||(e[15]=t("h3",{class:"text-lg font-semibold text-white"},"Orders by Status",-1)),o(l(i),{href:s.route("reports.orders"),class:"text-blue-400 hover:text-blue-300 text-sm font-medium"},{default:a(()=>e[14]||(e[14]=[w(" View Details → ",-1)])),_:1,__:[14]},8,["href"])]),o(v,{type:"doughnut",data:C.value,options:_,height:300},null,8,["data"])])]),t("div",X,[t("div",tt,[e[17]||(e[17]=t("h3",{class:"text-lg font-semibold text-white"},"Top Services by Revenue",-1)),o(l(i),{href:s.route("reports.services"),class:"text-blue-400 hover:text-blue-300 text-sm font-medium"},{default:a(()=>e[16]||(e[16]=[w(" View All Services → ",-1)])),_:1,__:[16]},8,["href"])]),o(v,{type:"bar",data:D.value,options:f,height:300},null,8,["data"])])])])]),_:1})],64))}};export{nt as default};
