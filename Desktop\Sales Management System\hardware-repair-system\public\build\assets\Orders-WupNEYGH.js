import{r as M,x as I,q as j,m as E,g as n,o as l,a as O,d as p,h as q,w as m,b as e,t as r,i as d,F as _,y as z,l as y,f as V,n as k,J,c as C,j as D,v as Q,I as L}from"./app-wnQ52fJE.js";import{_ as Y}from"./TechnicianLayout-BxKVOHiU.js";import"./ApplicationLogo-BIrQV_G-.js";import"./DropdownLink-BLptVuux.js";const G={class:"flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0"},K={class:"flex items-center space-x-3"},X={key:0,class:"inline-flex items-center px-2 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800 border border-blue-200"},Z={class:"text-gray-400 text-sm"},ee={class:"flex flex-col sm:flex-row items-start sm:items-center space-y-3 sm:space-y-0 sm:space-x-3"},te={class:"relative flex-1 max-w-md"},se={class:"flex items-center space-x-2"},re={class:"flex items-center space-x-2"},oe={class:"p-6 space-y-6"},le={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"},ne={class:"bg-gradient-to-br from-gray-900 to-gray-800 border border-gray-700 rounded-xl p-4 shadow-lg"},ie={class:"flex items-center justify-between"},ae={class:"text-2xl font-bold text-white"},de={class:"text-xs text-gray-500 mt-1"},ge={class:"bg-gradient-to-br from-gray-900 to-gray-800 border border-gray-700 rounded-xl p-4 shadow-lg"},ue={class:"flex items-center justify-between"},ce={class:"text-2xl font-bold text-white"},xe={key:0,class:"text-xs text-red-400 mt-1"},pe={key:1,class:"text-xs text-gray-500 mt-1"},me={class:"bg-gradient-to-br from-gray-900 to-gray-800 border border-gray-700 rounded-xl p-4 shadow-lg"},ve={class:"flex items-center justify-between"},be={class:"text-2xl font-bold text-white"},ye={class:"text-xs text-gray-500 mt-1"},he={class:"bg-gradient-to-br from-gray-900 to-gray-800 border border-gray-700 rounded-xl p-4 shadow-lg"},fe={class:"flex items-center justify-between"},we={class:"text-2xl font-bold text-white"},_e={class:"grid grid-cols-2 md:grid-cols-6 gap-4"},ke={class:"bg-gray-800 border border-gray-700 rounded-lg p-3 text-center"},Ce={class:"text-lg font-bold text-yellow-400"},Me={class:"bg-gray-800 border border-gray-700 rounded-lg p-3 text-center"},je={class:"text-lg font-bold text-blue-400"},Oe={class:"bg-gray-800 border border-gray-700 rounded-lg p-3 text-center"},Ve={class:"text-lg font-bold text-orange-400"},De={class:"bg-gray-800 border border-gray-700 rounded-lg p-3 text-center"},Pe={class:"text-lg font-bold text-green-400"},Be={class:"bg-gray-800 border border-gray-700 rounded-lg p-3 text-center"},Te={class:"text-lg font-bold text-purple-400"},Se={class:"bg-gray-800 border border-gray-700 rounded-lg p-3 text-center"},Ae={class:"text-lg font-bold text-red-400"},ze={class:"bg-gradient-to-br from-gray-900 to-gray-800 border border-gray-700 rounded-xl shadow-xl overflow-hidden"},Le={class:"px-6 py-4 border-b border-gray-700 bg-gradient-to-r from-gray-800 to-gray-700"},$e={class:"flex items-center justify-between"},He={class:"text-sm text-gray-400"},Fe={class:"p-6"},Ne={key:0,class:"space-y-6"},Re={class:"flex items-start justify-between mb-4"},Ue={class:"flex-1"},We={class:"flex items-center space-x-3 mb-3"},Ie={class:"text-xs text-gray-400"},Ee={class:"mb-4"},qe={class:"flex items-center justify-between mb-1"},Je={class:"text-xs text-gray-400"},Qe={class:"w-full bg-gray-700 rounded-full h-2"},Ye={class:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-4"},Ge={class:"bg-gray-700 rounded-lg p-3"},Ke={class:"text-white font-medium"},Xe={class:"text-xs text-gray-400 mt-1"},Ze={class:"bg-gray-700 rounded-lg p-3"},et={class:"text-white font-medium"},tt={class:"text-xs text-gray-400 mt-1"},st={class:"bg-gray-700 rounded-lg p-3"},rt={class:"text-white font-medium"},ot={class:"text-xs text-green-400 mt-1"},lt={class:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4"},nt={class:"bg-gray-700 rounded-lg p-3"},it={class:"text-white text-sm"},at={class:"text-xs text-gray-400 mt-1"},dt={key:0,class:"bg-gray-700 rounded-lg p-3"},gt={class:"text-white text-sm"},ut={class:"mb-4"},ct={class:"text-gray-300 text-sm bg-gray-700 rounded-lg p-3"},xt={key:0,class:"mb-4"},pt={class:"text-gray-300 text-sm bg-gray-700 rounded-lg p-3"},mt={class:"flex items-center justify-between pt-4 border-t border-gray-700"},vt={class:"flex items-center space-x-3"},bt=["onClick"],yt=["onClick"],ht=["onClick"],ft=["onClick"],wt={key:1,class:"text-center py-12"},_t={class:"flex flex-col items-center"},kt={class:"text-lg font-medium text-white mb-2"},Ct={class:"text-gray-400 text-sm mb-4"},Mt={key:0,class:"bg-gray-800 px-4 py-3 border-t border-gray-700 sm:px-6"},jt={class:"flex items-center justify-between"},Ot={class:"flex-1 flex justify-between sm:hidden"},Vt={class:"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between"},Dt={class:"text-sm text-gray-400"},Pt={class:"relative z-0 inline-flex rounded-md shadow-sm -space-x-px"},Bt=["innerHTML"],Lt={__name:"Orders",props:{repairOrders:Object,technician:Object,filters:Object,isAdminView:{type:Boolean,default:!1}},setup(a){const h=a,g=M(h.filters?.search||""),c=M(h.filters?.status||"all"),x=M(h.filters?.priority||"all");let P;I(g,o=>{clearTimeout(P),P=setTimeout(()=>{w()},300)});const f=(o,t)=>{j.patch(route("repair-orders.update-status",o),{status:t},{preserveScroll:!0,onSuccess:()=>{j.reload({only:["repairOrders"]})}})},w=()=>{const o={search:g.value||void 0,status:c.value!=="all"?c.value:void 0,priority:x.value!=="all"?x.value:void 0};j.get(route("technician.orders"),o,{preserveState:!0,replace:!0})},B=()=>{g.value="",c.value="all",x.value="all",w()},$=o=>{const t={pending:"bg-yellow-100 text-yellow-800 border-yellow-200",in_progress:"bg-blue-100 text-blue-800 border-blue-200",waiting_parts:"bg-orange-100 text-orange-800 border-orange-200",completed:"bg-green-100 text-green-800 border-green-200",cancelled:"bg-red-100 text-red-800 border-red-200",delivered:"bg-purple-100 text-purple-800 border-purple-200"};return t[o]||t.pending},H=o=>{const t={low:"bg-green-100 text-green-800 border-green-200",medium:"bg-yellow-100 text-yellow-800 border-yellow-200",high:"bg-orange-100 text-orange-800 border-orange-200",urgent:"bg-red-100 text-red-800 border-red-200"};return t[o]||t.medium},F=o=>new Date(o).toLocaleDateString(),N=o=>new Date(o).toLocaleString(),i=E(()=>{const o=h.repairOrders.data||[];return{total:o.length,pending:o.filter(t=>t.status==="pending").length,inProgress:o.filter(t=>t.status==="in_progress").length,waitingParts:o.filter(t=>t.status==="waiting_parts").length,completed:o.filter(t=>t.status==="completed").length,delivered:o.filter(t=>t.status==="delivered").length,cancelled:o.filter(t=>t.status==="cancelled").length,urgent:o.filter(t=>t.priority==="urgent").length,high:o.filter(t=>t.priority==="high").length,totalRevenue:o.filter(t=>["completed","delivered"].includes(t.status)).reduce((t,s)=>t+(s.total_cost||0),0),avgCompletionTime:R(o),completionRate:o.length>0?(o.filter(t=>["completed","delivered"].includes(t.status)).length/o.length*100).toFixed(1):0}}),R=o=>{const t=o.filter(u=>["completed","delivered"].includes(u.status)&&u.actual_completion);return t.length===0?0:(t.reduce((u,v)=>{const b=new Date(v.created_at),A=new Date(v.actual_completion),U=Math.abs(A-b),W=Math.ceil(U/(1e3*60*60*24));return u+W},0)/t.length).toFixed(1)},T=o=>({pending:10,in_progress:50,waiting_parts:70,completed:90,delivered:100,cancelled:0})[o.status]||0,S=o=>{const t=new Date,s=new Date(o),u=Math.abs(t-s),v=Math.floor(u/(1e3*60*60*24)),b=Math.floor(u%(1e3*60*60*24)/(1e3*60*60));return v>0?`${v}d ${b}h ago`:b>0?`${b}h ago`:`${Math.floor(u%36e5/6e4)}m ago`};return(o,t)=>(l(),n(_,null,[O(p(q),{title:"My Orders"}),O(Y,{isAdminView:a.isAdminView},{header:m(()=>[e("div",G,[e("div",null,[e("div",K,[t[4]||(t[4]=e("h2",{class:"text-2xl font-bold text-white mb-1"}," My Repair Orders ",-1)),a.isAdminView?(l(),n("span",X," Admin View ")):d("",!0)]),e("p",Z,r(a.isAdminView?"Orders you created as admin":"Manage your assigned repair orders"),1)]),e("div",ee,[e("div",te,[D(e("input",{"onUpdate:modelValue":t[0]||(t[0]=s=>g.value=s),type:"text",placeholder:"Search orders...",class:"w-full bg-gray-800 border border-gray-700 text-white placeholder-gray-400 rounded-lg px-4 py-2 pl-10 focus:ring-2 focus:ring-red-500 focus:border-transparent transition-colors duration-200"},null,512),[[Q,g.value]]),t[6]||(t[6]=e("svg",{class:"absolute left-3 top-2.5 w-4 h-4 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})],-1)),g.value?(l(),n("button",{key:0,onClick:t[1]||(t[1]=s=>g.value=""),class:"absolute right-3 top-2.5 text-gray-400 hover:text-white"},t[5]||(t[5]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))):d("",!0)]),e("div",se,[D(e("select",{"onUpdate:modelValue":t[2]||(t[2]=s=>c.value=s),onChange:w,class:"bg-gray-800 border border-gray-700 text-white rounded-lg px-4 py-2 focus:ring-2 focus:ring-red-500 focus:border-transparent"},t[7]||(t[7]=[e("option",{value:"all"},"All Status",-1),e("option",{value:"pending"},"Pending",-1),e("option",{value:"in_progress"},"In Progress",-1),e("option",{value:"waiting_parts"},"Waiting Parts",-1),e("option",{value:"completed"},"Completed",-1)]),544),[[L,c.value]]),D(e("select",{"onUpdate:modelValue":t[3]||(t[3]=s=>x.value=s),onChange:w,class:"bg-gray-800 border border-gray-700 text-white rounded-lg px-4 py-2 focus:ring-2 focus:ring-red-500 focus:border-transparent"},t[8]||(t[8]=[e("option",{value:"all"},"All Priority",-1),e("option",{value:"low"},"Low",-1),e("option",{value:"medium"},"Medium",-1),e("option",{value:"high"},"High",-1),e("option",{value:"urgent"},"Urgent",-1)]),544),[[L,x.value]])]),e("div",re,[g.value||c.value!=="all"||x.value!=="all"?(l(),n("button",{key:0,onClick:B,class:"bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors duration-200"}," Clear ")):d("",!0),a.isAdminView?(l(),C(p(y),{key:1,href:o.route("dashboard"),class:"bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center space-x-2"},{default:m(()=>t[9]||(t[9]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M10 19l-7-7m0 0l7-7m-7 7h18"})],-1),e("span",null,"Back to Admin",-1)])),_:1,__:[9]},8,["href"])):d("",!0)])])])]),default:m(()=>[e("div",oe,[e("div",le,[e("div",ne,[e("div",ie,[e("div",null,[t[10]||(t[10]=e("p",{class:"text-sm text-gray-400"},"Total Orders",-1)),e("p",ae,r(i.value.total),1),e("p",de,r(i.value.completionRate)+"% completion rate",1)]),t[11]||(t[11]=e("div",{class:"p-2 bg-blue-600 rounded-lg"},[e("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})])],-1))])]),e("div",ge,[e("div",ue,[e("div",null,[t[12]||(t[12]=e("p",{class:"text-sm text-gray-400"},"Active Orders",-1)),e("p",ce,r(i.value.pending+i.value.inProgress+i.value.waitingParts),1),i.value.urgent>0?(l(),n("p",xe,r(i.value.urgent)+" urgent",1)):(l(),n("p",pe,r(i.value.high)+" high priority",1))]),t[13]||(t[13]=e("div",{class:"p-2 bg-orange-600 rounded-lg"},[e("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 10V3L4 14h7v7l9-11h-7z"})])],-1))])]),e("div",me,[e("div",ve,[e("div",null,[t[14]||(t[14]=e("p",{class:"text-sm text-gray-400"},"Completed",-1)),e("p",be,r(i.value.completed+i.value.delivered),1),e("p",ye,"Avg: "+r(i.value.avgCompletionTime)+" days",1)]),t[15]||(t[15]=e("div",{class:"p-2 bg-green-600 rounded-lg"},[e("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1))])]),e("div",he,[e("div",fe,[e("div",null,[t[16]||(t[16]=e("p",{class:"text-sm text-gray-400"},"Total Revenue",-1)),e("p",we,"₱"+r(i.value.totalRevenue.toLocaleString()),1),t[17]||(t[17]=e("p",{class:"text-xs text-gray-500 mt-1"},"From completed orders",-1))]),t[18]||(t[18]=e("div",{class:"p-2 bg-purple-600 rounded-lg"},[e("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})])],-1))])])]),e("div",_e,[e("div",ke,[t[19]||(t[19]=e("p",{class:"text-xs text-gray-400"},"Pending",-1)),e("p",Ce,r(i.value.pending),1)]),e("div",Me,[t[20]||(t[20]=e("p",{class:"text-xs text-gray-400"},"In Progress",-1)),e("p",je,r(i.value.inProgress),1)]),e("div",Oe,[t[21]||(t[21]=e("p",{class:"text-xs text-gray-400"},"Waiting Parts",-1)),e("p",Ve,r(i.value.waitingParts),1)]),e("div",De,[t[22]||(t[22]=e("p",{class:"text-xs text-gray-400"},"Completed",-1)),e("p",Pe,r(i.value.completed),1)]),e("div",Be,[t[23]||(t[23]=e("p",{class:"text-xs text-gray-400"},"Delivered",-1)),e("p",Te,r(i.value.delivered),1)]),e("div",Se,[t[24]||(t[24]=e("p",{class:"text-xs text-gray-400"},"Cancelled",-1)),e("p",Ae,r(i.value.cancelled),1)])]),e("div",ze,[e("div",Le,[e("div",$e,[t[25]||(t[25]=e("div",{class:"flex items-center space-x-3"},[e("div",{class:"p-2 bg-red-600 rounded-lg"},[e("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"})])]),e("h3",{class:"text-lg font-semibold text-white"},"Assigned Orders")],-1)),e("span",He,r(a.repairOrders.data?.length||0)+" orders",1)])]),e("div",Fe,[a.repairOrders.data?.length>0?(l(),n("div",Ne,[(l(!0),n(_,null,z(a.repairOrders.data,s=>(l(),n("div",{key:s.id,class:"bg-gradient-to-br from-gray-800 to-gray-900 rounded-xl p-6 border border-gray-700 hover:border-gray-600 transition-all duration-200 shadow-lg"},[e("div",Re,[e("div",Ue,[e("div",We,[O(p(y),{href:o.route("repair-orders.show",s.id),class:"text-xl font-semibold text-white hover:text-red-300 transition-colors duration-200"},{default:m(()=>[V(r(s.order_number),1)]),_:2},1032,["href"]),e("span",{class:k(["inline-flex items-center px-2.5 py-0.5 text-xs font-medium rounded-full border",H(s.priority)])},r(s.priority.toUpperCase()),3),e("span",{class:k(["inline-flex items-center px-2.5 py-0.5 text-xs font-medium rounded-full border",$(s.status)])},r(s.status.replace("_"," ").toUpperCase()),3),e("span",Ie,r(S(s.created_at)),1)]),e("div",Ee,[e("div",qe,[t[26]||(t[26]=e("span",{class:"text-xs text-gray-400"},"Progress",-1)),e("span",Je,r(T(s))+"%",1)]),e("div",Qe,[e("div",{class:"bg-gradient-to-r from-red-500 to-red-600 h-2 rounded-full transition-all duration-300",style:J({width:T(s)+"%"})},null,4)])]),e("div",Ye,[e("div",Ge,[t[27]||(t[27]=e("p",{class:"text-xs text-gray-400 mb-1"},"Customer",-1)),e("p",Ke,r(s.customer?.full_name),1),e("p",Xe,r(s.customer?.phone),1)]),e("div",Ze,[t[28]||(t[28]=e("p",{class:"text-xs text-gray-400 mb-1"},"Device",-1)),e("p",et,r(s.device?.brand)+" "+r(s.device?.model),1),e("p",tt,r(s.device?.device_type?.name),1)]),e("div",st,[t[29]||(t[29]=e("p",{class:"text-xs text-gray-400 mb-1"},"Service",-1)),e("p",rt,r(s.service?.name),1),e("p",ot,"₱"+r(s.total_cost?.toLocaleString()),1)])]),e("div",lt,[e("div",nt,[t[30]||(t[30]=e("p",{class:"text-xs text-gray-400 mb-1"},"Created",-1)),e("p",it,r(N(s.created_at)),1),e("p",at,r(S(s.created_at)),1)]),s.estimated_completion?(l(),n("div",dt,[t[31]||(t[31]=e("p",{class:"text-xs text-gray-400 mb-1"},"Estimated Completion",-1)),e("p",gt,r(F(s.estimated_completion)),1),e("p",{class:k(["text-xs",new Date(s.estimated_completion)<new Date?"text-red-400":"text-green-400"])},r(new Date(s.estimated_completion)<new Date?"Overdue":"On Track"),3)])):d("",!0)]),e("div",ut,[t[32]||(t[32]=e("p",{class:"text-sm text-gray-400 mb-2"},"Issue Description",-1)),e("p",ct,r(s.issue_description),1)]),s.customer_notes?(l(),n("div",xt,[t[33]||(t[33]=e("p",{class:"text-sm text-gray-400 mb-2"},"Customer Notes",-1)),e("p",pt,r(s.customer_notes),1)])):d("",!0)])]),e("div",mt,[t[38]||(t[38]=e("div",{class:"flex items-center space-x-2"},null,-1)),e("div",vt,[s.status==="pending"?(l(),n("button",{key:0,onClick:u=>f(s.id,"in_progress"),class:"px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm rounded-lg transition-colors duration-200 flex items-center space-x-2"},t[34]||(t[34]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h8m2-10V7a2 2 0 01-2 2H5a2 2 0 01-2-2V4a2 2 0 012-2h14a2 2 0 012 2z"})],-1),e("span",null,"Start Work",-1)]),8,bt)):d("",!0),s.status==="in_progress"?(l(),n("button",{key:1,onClick:u=>f(s.id,"waiting_parts"),class:"px-4 py-2 bg-orange-600 hover:bg-orange-700 text-white text-sm rounded-lg transition-colors duration-200 flex items-center space-x-2"},t[35]||(t[35]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})],-1),e("span",null,"Wait for Parts",-1)]),8,yt)):d("",!0),s.status==="in_progress"?(l(),n("button",{key:2,onClick:u=>f(s.id,"completed"),class:"px-4 py-2 bg-green-600 hover:bg-green-700 text-white text-sm rounded-lg transition-colors duration-200 flex items-center space-x-2"},t[36]||(t[36]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})],-1),e("span",null,"Mark Complete",-1)]),8,ht)):d("",!0),s.status==="waiting_parts"?(l(),n("button",{key:3,onClick:u=>f(s.id,"in_progress"),class:"px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm rounded-lg transition-colors duration-200 flex items-center space-x-2"},t[37]||(t[37]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 10V3L4 14h7v7l9-11h-7z"})],-1),e("span",null,"Resume Work",-1)]),8,ft)):d("",!0)])])]))),128))])):(l(),n("div",wt,[e("div",_t,[t[39]||(t[39]=e("div",{class:"w-16 h-16 bg-gray-800 rounded-full flex items-center justify-center mb-4"},[e("svg",{class:"w-8 h-8 text-gray-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"1",d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})])],-1)),e("h3",kt,r(g.value?"No orders found":"No orders assigned"),1),e("p",Ct,r(g.value?`No orders match "${g.value}"`:"You have no repair orders assigned at the moment"),1),g.value?(l(),n("button",{key:0,onClick:B,class:"text-red-400 hover:text-red-300 text-sm font-medium"}," Clear search and show all orders → ")):d("",!0)])]))]),a.repairOrders.links?(l(),n("div",Mt,[e("div",jt,[e("div",Ot,[a.repairOrders.prev_page_url?(l(),C(p(y),{key:0,href:a.repairOrders.prev_page_url,class:"relative inline-flex items-center px-4 py-2 border border-gray-600 text-sm font-medium rounded-md text-gray-300 bg-gray-800 hover:bg-gray-700"},{default:m(()=>t[40]||(t[40]=[V(" Previous ",-1)])),_:1,__:[40]},8,["href"])):d("",!0),a.repairOrders.next_page_url?(l(),C(p(y),{key:1,href:a.repairOrders.next_page_url,class:"ml-3 relative inline-flex items-center px-4 py-2 border border-gray-600 text-sm font-medium rounded-md text-gray-300 bg-gray-800 hover:bg-gray-700"},{default:m(()=>t[41]||(t[41]=[V(" Next ",-1)])),_:1,__:[41]},8,["href"])):d("",!0)]),e("div",Vt,[e("div",null,[e("p",Dt," Showing "+r(a.repairOrders.from)+" to "+r(a.repairOrders.to)+" of "+r(a.repairOrders.total)+" results ",1)]),e("div",null,[e("nav",Pt,[(l(!0),n(_,null,z(a.repairOrders.links,s=>(l(),n(_,{key:s.label},[s.url?(l(),C(p(y),{key:0,href:s.url,class:k(["relative inline-flex items-center px-2 py-2 border text-sm font-medium",{"z-10 bg-red-600 border-red-600 text-white":s.active,"bg-gray-800 border-gray-600 text-gray-300 hover:bg-gray-700":!s.active}]),innerHTML:s.label},null,8,["href","class","innerHTML"])):(l(),n("span",{key:1,class:"relative inline-flex items-center px-2 py-2 border text-sm font-medium cursor-not-allowed opacity-50 bg-gray-800 border-gray-600 text-gray-500",innerHTML:s.label},null,8,Bt))],64))),128))])])])])])):d("",!0)])])]),_:1},8,["isAdminView"])],64))}};export{Lt as default};
