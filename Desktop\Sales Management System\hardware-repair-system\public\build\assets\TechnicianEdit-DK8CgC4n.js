import{_ as l}from"./TechnicianLayout-BxKVOHiU.js";import c from"./DeleteUserForm-Ce47N0tY.js";import m from"./UpdatePasswordForm-dGDm8t3y.js";import g from"./UpdateProfileInformationForm-sbkI9DxP.js";import{g as o,o as d,a as i,d as x,h as u,w as n,b as e,i as a,t as r,n as b,f,F as h}from"./app-wnQ52fJE.js";import"./ApplicationLogo-BIrQV_G-.js";import"./DropdownLink-BLptVuux.js";import"./TextInput-jgbPOCPR.js";import"./PrimaryButton-BTRdXBgS.js";const v={class:"flex items-center justify-between"},y={class:"flex items-center space-x-4"},p={class:"text-2xl font-bold text-white"},w={key:0,class:"text-sm text-blue-300 ml-2"},_={class:"p-6"},k={class:"max-w-4xl mx-auto space-y-6"},V={class:"bg-gradient-to-br from-gray-900 to-gray-800 border border-gray-700 rounded-xl shadow-xl overflow-hidden"},j={class:"p-6"},z={key:0,class:"bg-gradient-to-br from-gray-900 to-gray-800 border border-gray-700 rounded-xl shadow-xl overflow-hidden"},B={class:"p-6"},M={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},A={class:"space-y-4"},C={class:"flex items-center justify-between p-4 bg-gray-800 rounded-lg border border-gray-700"},T={class:"text-gray-400 text-sm"},E={class:"flex items-center justify-between p-4 bg-gray-800 rounded-lg border border-gray-700"},D={class:"text-gray-400 text-sm"},I={class:"space-y-4"},N={class:"flex items-center justify-between p-4 bg-gray-800 rounded-lg border border-gray-700"},P={class:"text-gray-400 text-sm"},S={class:"bg-gradient-to-br from-gray-900 to-gray-800 border border-gray-700 rounded-xl shadow-xl overflow-hidden"},$={class:"p-6"},F={class:"bg-gradient-to-br from-red-900 to-red-800 border border-red-700 rounded-xl shadow-xl overflow-hidden"},L={class:"p-6"},W={__name:"TechnicianEdit",props:{mustVerifyEmail:{type:Boolean},status:{type:String},technician:{type:Object,required:!0},isAdminView:{type:Boolean,default:!1}},setup(s){return(q,t)=>(d(),o(h,null,[i(x(u),{title:"Technician Profile"}),i(l,{isAdminView:s.isAdminView},{header:n(()=>[e("div",v,[e("div",y,[t[2]||(t[2]=e("div",{class:"w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center shadow-lg"},[e("svg",{class:"w-6 h-6 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})])],-1)),e("div",null,[e("h2",p,[t[0]||(t[0]=f(" Technician Profile ",-1)),s.isAdminView?(d(),o("span",w,"(Admin View)")):a("",!0)]),t[1]||(t[1]=e("p",{class:"text-gray-400 text-sm"},"Manage your technician account settings",-1))])])])]),default:n(()=>[e("div",_,[e("div",k,[e("div",V,[t[3]||(t[3]=e("div",{class:"px-6 py-4 border-b border-gray-700 bg-gradient-to-r from-gray-800 to-gray-700"},[e("div",{class:"flex items-center space-x-3"},[e("div",{class:"p-2 bg-red-600 rounded-lg"},[e("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})])]),e("h3",{class:"text-lg font-semibold text-white"},"Profile Information")])],-1)),e("div",j,[i(g,{"must-verify-email":s.mustVerifyEmail,status:s.status,class:"max-w-xl"},null,8,["must-verify-email","status"])])]),s.technician?(d(),o("div",z,[t[8]||(t[8]=e("div",{class:"px-6 py-4 border-b border-gray-700 bg-gradient-to-r from-gray-800 to-gray-700"},[e("div",{class:"flex items-center space-x-3"},[e("div",{class:"p-2 bg-blue-600 rounded-lg"},[e("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"})])]),e("h3",{class:"text-lg font-semibold text-white"},"Technician Details")])],-1)),e("div",B,[e("div",M,[e("div",A,[e("div",C,[e("div",null,[t[4]||(t[4]=e("h4",{class:"text-white font-medium"},"Employee ID",-1)),e("p",T,r(s.technician.employee_id),1)])]),e("div",E,[e("div",null,[t[5]||(t[5]=e("h4",{class:"text-white font-medium"},"Specialization",-1)),e("p",D,r(s.technician.specialization),1)])])]),e("div",I,[e("div",N,[e("div",null,[t[6]||(t[6]=e("h4",{class:"text-white font-medium"},"Status",-1)),e("p",P,r(s.technician.is_active?"Active":"Inactive"),1)]),e("span",{class:b(["inline-flex items-center px-3 py-1 text-xs font-medium rounded-full",s.technician.is_active?"bg-green-100 text-green-800 border border-green-200":"bg-red-100 text-red-800 border border-red-200"])},r(s.technician.is_active?"Active":"Inactive"),3)]),t[7]||(t[7]=e("div",{class:"flex items-center justify-between p-4 bg-gray-800 rounded-lg border border-gray-700"},[e("div",null,[e("h4",{class:"text-white font-medium"},"Role"),e("p",{class:"text-gray-400 text-sm"},"Technician")]),e("span",{class:"inline-flex items-center px-3 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800 border border-blue-200"}," Technician ")],-1))])])])])):a("",!0),e("div",S,[t[9]||(t[9]=e("div",{class:"px-6 py-4 border-b border-gray-700 bg-gradient-to-r from-gray-800 to-gray-700"},[e("div",{class:"flex items-center space-x-3"},[e("div",{class:"p-2 bg-yellow-600 rounded-lg"},[e("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"})])]),e("h3",{class:"text-lg font-semibold text-white"},"Update Password")])],-1)),e("div",$,[i(m,{class:"max-w-xl"})])]),e("div",F,[t[10]||(t[10]=e("div",{class:"px-6 py-4 border-b border-red-700 bg-gradient-to-r from-red-800 to-red-700"},[e("div",{class:"flex items-center space-x-3"},[e("div",{class:"p-2 bg-red-600 rounded-lg"},[e("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})])]),e("h3",{class:"text-lg font-semibold text-white"},"Danger Zone")])],-1)),e("div",L,[i(c,{class:"max-w-xl"})])])])])]),_:1},8,["isAdminView"])],64))}};export{W as default};
