import{_ as p}from"./AuthenticatedLayout-D449e8ZD.js";import{_}from"./TechnicianModal-VAFlpPWT.js";import{r as k,g as n,o as r,a,d as x,h as C,w as d,b as t,t as o,i as m,n as b,l as u,f,F as v,y as j,q as M}from"./app-wnQ52fJE.js";import"./ApplicationLogo-BIrQV_G-.js";import"./DropdownLink-BLptVuux.js";const V={class:"flex items-center justify-between"},z={class:"flex items-center space-x-4"},B={class:"w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center shadow-lg"},D={class:"text-lg font-bold text-white"},N={class:"text-2xl font-bold text-white"},E={class:"text-gray-400 text-sm"},S={class:"p-6 space-y-8"},H={class:"grid grid-cols-1 md:grid-cols-4 gap-6"},T={class:"bg-gradient-to-br from-gray-900 to-gray-800 border border-gray-700 rounded-xl p-6 shadow-xl"},A={class:"flex items-center justify-between"},I={class:"text-3xl font-bold text-white"},P={class:"bg-gradient-to-br from-gray-900 to-gray-800 border border-gray-700 rounded-xl p-6 shadow-xl"},F={class:"flex items-center justify-between"},O={class:"text-3xl font-bold text-white"},L={class:"bg-gradient-to-br from-gray-900 to-gray-800 border border-gray-700 rounded-xl p-6 shadow-xl"},R={class:"flex items-center justify-between"},$={class:"text-3xl font-bold text-white"},q={class:"bg-gradient-to-br from-gray-900 to-gray-800 border border-gray-700 rounded-xl p-6 shadow-xl"},U={class:"flex items-center justify-between"},G={class:"text-3xl font-bold text-white"},J={class:"grid grid-cols-1 lg:grid-cols-3 gap-8"},K={class:"lg:col-span-1"},Q={class:"bg-gradient-to-br from-gray-900 to-gray-800 border border-gray-700 rounded-xl shadow-xl"},W={class:"p-6 space-y-6"},X={class:"text-white font-medium"},Y={class:"text-white font-medium"},Z={key:0},tt={class:"text-white font-medium"},et={class:"text-white font-medium"},st={class:"text-white font-medium"},ot={class:"text-white font-medium"},it={key:1},nt={class:"text-white text-sm bg-gray-800 rounded-lg p-3 mt-1"},rt={key:2},lt={class:"text-white text-sm bg-gray-800 rounded-lg p-3 mt-1"},at={key:3},dt={class:"text-white text-sm bg-gray-800 rounded-lg p-3 mt-1"},ct={class:"lg:col-span-2"},xt={class:"bg-gradient-to-br from-gray-900 to-gray-800 border border-gray-700 rounded-xl shadow-xl"},mt={class:"px-6 py-4 border-b border-gray-700 bg-gradient-to-r from-gray-800 to-gray-700 rounded-t-xl"},gt={class:"flex items-center justify-between"},ut={class:"p-6"},ht={key:0,class:"space-y-4"},bt={class:"flex items-start justify-between mb-3"},ft={class:"flex-1"},vt={class:"flex items-center space-x-3 mb-2"},yt={class:"text-sm text-gray-300 mb-1"},wt={class:"text-sm text-gray-400"},pt={class:"text-xs text-gray-500 mt-2"},_t={class:"text-right"},kt={class:"text-sm text-gray-400"},Ct={class:"text-sm font-medium text-white"},jt={key:1,class:"text-center py-8"},Mt={class:"bg-gradient-to-br from-gray-900 to-gray-800 border border-gray-700 rounded-xl shadow-xl"},Vt={class:"p-6"},zt={class:"grid grid-cols-1 md:grid-cols-3 gap-6"},Bt={class:"text-center"},Dt={class:"text-3xl font-bold text-white mb-2"},Nt={class:"text-center"},Et={class:"text-3xl font-bold text-white mb-2"},St={class:"text-center"},Ht={class:"text-3xl font-bold text-white mb-2"},Ot={__name:"Show",props:{technician:Object,stats:Object},setup(s){const c=k(!1),y=()=>{c.value=!0},w=()=>{c.value=!1,M.reload({only:["technician"]})},h=l=>new Date(l).toLocaleDateString(),g=l=>new Intl.NumberFormat("en-PH",{style:"currency",currency:"PHP",minimumFractionDigits:2}).format(l||0);return(l,e)=>(r(),n(v,null,[a(x(C),{title:`${s.technician.user?.name} - Technician Details`},null,8,["title"]),a(p,null,{header:d(()=>[t("div",V,[t("div",z,[a(x(u),{href:l.route("technicians.index"),class:"text-gray-400 hover:text-white transition-colors duration-200"},{default:d(()=>e[1]||(e[1]=[t("svg",{class:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 19l-7-7 7-7"})],-1)])),_:1,__:[1]},8,["href"]),t("div",B,[t("span",D,o(s.technician.user?.name?.charAt(0)),1)]),t("div",null,[t("h2",N,o(s.technician.user?.name),1),t("p",E,o(s.technician.specialization)+" • Employee ID: "+o(s.technician.employee_id),1)])]),t("div",{class:"flex items-center space-x-3"},[t("button",{onClick:y,class:"bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center space-x-2"},e[2]||(e[2]=[t("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})],-1),t("span",null,"Edit",-1)]))])])]),default:d(()=>[t("div",S,[t("div",H,[t("div",T,[t("div",A,[t("div",null,[e[3]||(e[3]=t("p",{class:"text-sm text-gray-400"},"Total Orders",-1)),t("p",I,o(s.stats.total_orders),1)]),e[4]||(e[4]=t("div",{class:"p-3 bg-blue-600 rounded-lg"},[t("svg",{class:"w-6 h-6 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})])],-1))])]),t("div",P,[t("div",F,[t("div",null,[e[5]||(e[5]=t("p",{class:"text-sm text-gray-400"},"Completed",-1)),t("p",O,o(s.stats.completed_orders),1)]),e[6]||(e[6]=t("div",{class:"p-3 bg-green-600 rounded-lg"},[t("svg",{class:"w-6 h-6 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1))])]),t("div",L,[t("div",R,[t("div",null,[e[7]||(e[7]=t("p",{class:"text-sm text-gray-400"},"In Progress",-1)),t("p",$,o(s.stats.in_progress_orders),1)]),e[8]||(e[8]=t("div",{class:"p-3 bg-orange-600 rounded-lg"},[t("svg",{class:"w-6 h-6 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 10V3L4 14h7v7l9-11h-7z"})])],-1))])]),t("div",q,[t("div",U,[t("div",null,[e[9]||(e[9]=t("p",{class:"text-sm text-gray-400"},"Total Revenue",-1)),t("p",G,o(g(s.stats.total_revenue)),1)]),e[10]||(e[10]=t("div",{class:"p-3 bg-purple-600 rounded-lg"},[t("span",{class:"text-white text-xl font-bold"},"₱")],-1))])])]),t("div",J,[t("div",K,[t("div",Q,[e[21]||(e[21]=t("div",{class:"px-6 py-4 border-b border-gray-700 bg-gradient-to-r from-gray-800 to-gray-700 rounded-t-xl"},[t("h3",{class:"text-lg font-semibold text-white"},"Technician Information")],-1)),t("div",W,[t("div",null,[e[11]||(e[11]=t("label",{class:"text-sm font-medium text-gray-400"},"Full Name",-1)),t("p",X,o(s.technician.user?.name),1)]),t("div",null,[e[12]||(e[12]=t("label",{class:"text-sm font-medium text-gray-400"},"Email",-1)),t("p",Y,o(s.technician.user?.email),1)]),s.technician.phone?(r(),n("div",Z,[e[13]||(e[13]=t("label",{class:"text-sm font-medium text-gray-400"},"Phone",-1)),t("p",tt,o(s.technician.phone),1)])):m("",!0),t("div",null,[e[14]||(e[14]=t("label",{class:"text-sm font-medium text-gray-400"},"Employee ID",-1)),t("p",et,o(s.technician.employee_id),1)]),t("div",null,[e[15]||(e[15]=t("label",{class:"text-sm font-medium text-gray-400"},"Specialization",-1)),t("p",st,o(s.technician.specialization),1)]),t("div",null,[e[16]||(e[16]=t("label",{class:"text-sm font-medium text-gray-400"},"Hire Date",-1)),t("p",ot,o(h(s.technician.hire_date)),1)]),t("div",null,[e[17]||(e[17]=t("label",{class:"text-sm font-medium text-gray-400"},"Status",-1)),t("span",{class:b(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border",s.technician.is_active?"bg-green-100 text-green-800 border-green-200":"bg-red-100 text-red-800 border-red-200"])},o(s.technician.is_active?"Active":"Inactive"),3)]),s.technician.certifications?(r(),n("div",it,[e[18]||(e[18]=t("label",{class:"text-sm font-medium text-gray-400"},"Certifications",-1)),t("p",nt,o(s.technician.certifications),1)])):m("",!0),s.technician.skills?(r(),n("div",rt,[e[19]||(e[19]=t("label",{class:"text-sm font-medium text-gray-400"},"Skills & Expertise",-1)),t("p",lt,o(s.technician.skills),1)])):m("",!0),s.technician.notes?(r(),n("div",at,[e[20]||(e[20]=t("label",{class:"text-sm font-medium text-gray-400"},"Notes",-1)),t("p",dt,o(s.technician.notes),1)])):m("",!0)])])]),t("div",ct,[t("div",xt,[t("div",mt,[t("div",gt,[e[23]||(e[23]=t("h3",{class:"text-lg font-semibold text-white"},"Recent Repair Orders",-1)),a(x(u),{href:l.route("repair-orders.index",{technician:s.technician.id}),class:"text-red-400 hover:text-red-300 text-sm font-medium"},{default:d(()=>e[22]||(e[22]=[f(" View All → ",-1)])),_:1,__:[22]},8,["href"])])]),t("div",ut,[s.technician.repair_orders?.length>0?(r(),n("div",ht,[(r(!0),n(v,null,j(s.technician.repair_orders,i=>(r(),n("div",{key:i.id,class:"bg-gray-800 rounded-lg p-4 border border-gray-700 hover:border-gray-600 transition-colors duration-200"},[t("div",bt,[t("div",ft,[t("div",vt,[a(x(u),{href:l.route("repair-orders.show",i.id),class:"text-lg font-semibold text-white hover:text-red-300 transition-colors duration-200"},{default:d(()=>[f(o(i.order_number),1)]),_:2},1032,["href"]),t("span",{class:b(["inline-flex items-center px-2 py-1 text-xs font-medium rounded-full border",{"bg-yellow-100 text-yellow-800 border-yellow-200":i.status==="pending","bg-blue-100 text-blue-800 border-blue-200":i.status==="in_progress","bg-orange-100 text-orange-800 border-orange-200":i.status==="waiting_parts","bg-green-100 text-green-800 border-green-200":i.status==="completed","bg-red-100 text-red-800 border-red-200":i.status==="cancelled","bg-purple-100 text-purple-800 border-purple-200":i.status==="delivered"}])},o(i.status.replace("_"," ").toUpperCase()),3)]),t("p",yt,o(i.customer?.full_name),1),t("p",wt,o(i.device?.brand)+" "+o(i.device?.model),1),t("p",pt,o(i.service?.name),1)]),t("div",_t,[t("p",kt,o(h(i.created_at)),1),t("p",Ct,o(g(i.total_cost)),1)])])]))),128))])):(r(),n("div",jt,e[24]||(e[24]=[t("svg",{class:"w-12 h-12 text-gray-600 mx-auto mb-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"1",d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})],-1),t("p",{class:"text-gray-400 text-sm"},"No repair orders assigned yet",-1)])))])])])]),t("div",Mt,[e[28]||(e[28]=t("div",{class:"px-6 py-4 border-b border-gray-700 bg-gradient-to-r from-gray-800 to-gray-700 rounded-t-xl"},[t("h3",{class:"text-lg font-semibold text-white"},"Performance Metrics")],-1)),t("div",Vt,[t("div",zt,[t("div",Bt,[t("div",Dt,o(s.stats.avg_completion_time?Math.round(s.stats.avg_completion_time):"N/A"),1),e[25]||(e[25]=t("div",{class:"text-sm text-gray-400"},"Average Completion Time (days)",-1))]),t("div",Nt,[t("div",Et,o(s.stats.completed_orders>0?Math.round(s.stats.completed_orders/s.stats.total_orders*100):0)+"% ",1),e[26]||(e[26]=t("div",{class:"text-sm text-gray-400"},"Completion Rate",-1))]),t("div",St,[t("div",Ht,o(g(s.stats.total_revenue/Math.max(s.stats.completed_orders,1))),1),e[27]||(e[27]=t("div",{class:"text-sm text-gray-400"},"Average Order Value",-1))])])])])]),a(_,{show:c.value,technician:s.technician,onClose:e[0]||(e[0]=i=>c.value=!1),onSaved:w},null,8,["show","technician"])]),_:1})],64))}};export{Ot as default};
