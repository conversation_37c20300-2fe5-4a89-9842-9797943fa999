import{_ as k}from"./AuthenticatedLayout-D449e8ZD.js";import{g as d,o as g,a,d as n,h as _,w as i,b as e,t as r,n as h,l as m,f as x,i as b,F as p,y as f}from"./app-wnQ52fJE.js";import"./ApplicationLogo-BIrQV_G-.js";import"./DropdownLink-BLptVuux.js";const S={class:"py-12"},C={class:"max-w-7xl mx-auto sm:px-6 lg:px-8 space-y-6"},j={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"},M={class:"bg-gray-800 rounded-xl p-6 border border-gray-700"},R={class:"flex items-center justify-between"},V={class:"text-2xl font-bold text-white"},B={class:"bg-gray-800 rounded-xl p-6 border border-gray-700"},T={class:"flex items-center justify-between"},Y={class:"text-2xl font-bold text-white"},z={class:"bg-gray-800 rounded-xl p-6 border border-gray-700"},A={class:"flex items-center justify-between"},F={class:"text-2xl font-bold text-white"},N={class:"bg-gray-800 rounded-xl p-6 border border-gray-700"},G={class:"flex items-center justify-between"},D={class:"text-2xl font-bold text-white"},E={class:"grid grid-cols-1 md:grid-cols-3 gap-6"},O={class:"bg-gray-800 rounded-xl p-6 border border-gray-700"},P={class:"space-y-3"},$={class:"bg-gray-800 rounded-xl p-6 border border-gray-700"},H={class:"space-y-3"},L={class:"bg-gray-800 rounded-xl p-6 border border-gray-700"},I={class:"space-y-3"},U={class:"grid grid-cols-1 lg:grid-cols-2 gap-6"},q={class:"bg-gray-800 rounded-xl p-6 border border-gray-700"},J={class:"space-y-3"},K={class:"font-medium text-white"},Q={class:"text-sm text-gray-400"},W={class:"text-right"},X={class:"font-semibold text-green-400"},Z={key:0,class:"text-center text-gray-400 py-4"},ee={class:"bg-gray-800 rounded-xl p-6 border border-gray-700"},te={class:"space-y-3"},se={class:"font-medium text-white"},re={class:"text-sm text-gray-400"},oe={class:"text-right"},le={class:"font-semibold text-green-400"},ne={key:0,class:"text-center text-gray-400 py-4"},ue={__name:"Index",props:{salesSummary:Object,expensesSummary:Object,topServices:Array,topCustomers:Array},setup(s){const v=new Date().getFullYear(),w=new Date().getMonth()+1,u=o=>"₱"+parseFloat(o||0).toLocaleString("en-US",{minimumFractionDigits:2}),c=o=>`${o>=0?"+":""}${o.toFixed(1)}%`,y=o=>o>=0?"text-green-400":"text-red-400";return(o,t)=>(g(),d(p,null,[a(n(_),{title:"Reports"}),a(k,null,{header:i(()=>t[0]||(t[0]=[e("div",{class:"flex items-center justify-between"},[e("h2",{class:"text-xl font-semibold leading-tight text-white"}," Reports & Analytics "),e("div",{class:"text-sm text-gray-400"}," Business insights and financial reports ")],-1)])),default:i(()=>[e("div",S,[e("div",C,[e("div",j,[e("div",M,[e("div",R,[e("div",null,[t[1]||(t[1]=e("p",{class:"text-sm font-medium text-gray-400"},"This Month Sales",-1)),e("p",V,r(u(s.salesSummary.thisMonth)),1),e("p",{class:h(["text-sm mt-1",y(s.salesSummary.monthlyGrowth)])},r(c(s.salesSummary.monthlyGrowth))+" from last month ",3)]),t[2]||(t[2]=e("div",{class:"p-3 bg-green-900/50 rounded-lg"},[e("svg",{class:"w-6 h-6 text-green-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})])],-1))])]),e("div",B,[e("div",T,[e("div",null,[t[3]||(t[3]=e("p",{class:"text-sm font-medium text-gray-400"},"This Year Sales",-1)),e("p",Y,r(u(s.salesSummary.thisYear)),1),e("p",{class:h(["text-sm mt-1",y(s.salesSummary.yearlyGrowth)])},r(c(s.salesSummary.yearlyGrowth))+" from last year ",3)]),t[4]||(t[4]=e("div",{class:"p-3 bg-blue-900/50 rounded-lg"},[e("svg",{class:"w-6 h-6 text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"})])],-1))])]),e("div",z,[e("div",A,[e("div",null,[t[5]||(t[5]=e("p",{class:"text-sm font-medium text-gray-400"},"This Month Expenses",-1)),e("p",F,r(u(s.expensesSummary.thisMonth)),1),e("p",{class:h(["text-sm mt-1",y(-s.expensesSummary.monthlyChange)])},r(c(s.expensesSummary.monthlyChange))+" from last month ",3)]),t[6]||(t[6]=e("div",{class:"p-3 bg-red-900/50 rounded-lg"},[e("svg",{class:"w-6 h-6 text-red-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 17h8m0 0V9m0 8l-8-8-4 4-6-6"})])],-1))])]),e("div",N,[e("div",G,[e("div",null,[t[7]||(t[7]=e("p",{class:"text-sm font-medium text-gray-400"},"This Year Expenses",-1)),e("p",D,r(u(s.expensesSummary.thisYear)),1),e("p",{class:h(["text-sm mt-1",y(-s.expensesSummary.yearlyChange)])},r(c(s.expensesSummary.yearlyChange))+" from last year ",3)]),t[8]||(t[8]=e("div",{class:"p-3 bg-orange-900/50 rounded-lg"},[e("svg",{class:"w-6 h-6 text-orange-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 14l-7 7m0 0l-7-7m7 7V3"})])],-1))])])]),e("div",E,[e("div",O,[t[11]||(t[11]=e("div",{class:"flex items-center mb-4"},[e("div",{class:"p-2 bg-green-900/50 rounded-lg mr-3"},[e("svg",{class:"w-6 h-6 text-green-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"})])]),e("div",null,[e("h3",{class:"text-lg font-semibold text-white"},"Sales Reports"),e("p",{class:"text-sm text-gray-400"},"Revenue analysis and trends")])],-1)),e("div",P,[a(n(m),{href:o.route("reports.revenue"),class:"block w-full bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-lg text-center transition-colors duration-200"},{default:i(()=>t[9]||(t[9]=[x(" Revenue Report ",-1)])),_:1,__:[9]},8,["href"]),a(n(m),{href:o.route("reports.orders"),class:"block w-full bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-lg text-center transition-colors duration-200"},{default:i(()=>t[10]||(t[10]=[x(" Orders Report ",-1)])),_:1,__:[10]},8,["href"])])]),e("div",$,[t[14]||(t[14]=e("div",{class:"flex items-center mb-4"},[e("div",{class:"p-2 bg-red-900/50 rounded-lg mr-3"},[e("svg",{class:"w-6 h-6 text-red-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 17h8m0 0V9m0 8l-8-8-4 4-6-6"})])]),e("div",null,[e("h3",{class:"text-lg font-semibold text-white"},"Expenses Reports"),e("p",{class:"text-sm text-gray-400"},"Parts costs and expenses")])],-1)),e("div",H,[a(n(m),{href:o.route("reports.customers"),class:"block w-full bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-lg text-center transition-colors duration-200"},{default:i(()=>t[12]||(t[12]=[x(" Customers Report ",-1)])),_:1,__:[12]},8,["href"]),a(n(m),{href:o.route("reports.services"),class:"block w-full bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-lg text-center transition-colors duration-200"},{default:i(()=>t[13]||(t[13]=[x(" Services Report ",-1)])),_:1,__:[13]},8,["href"])])]),e("div",L,[t[17]||(t[17]=e("div",{class:"flex items-center mb-4"},[e("div",{class:"p-2 bg-blue-900/50 rounded-lg mr-3"},[e("svg",{class:"w-6 h-6 text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})])]),e("div",null,[e("h3",{class:"text-lg font-semibold text-white"},"Profit Analysis"),e("p",{class:"text-sm text-gray-400"},"Revenue vs expenses")])],-1)),e("div",I,[a(n(m),{href:o.route("reports.profit-analysis",{period:"monthly",year:n(v),month:w}),class:"block w-full bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-lg text-center transition-colors duration-200"},{default:i(()=>t[15]||(t[15]=[x(" Current Month Analysis ",-1)])),_:1,__:[15]},8,["href"]),a(n(m),{href:o.route("reports.profit-analysis",{period:"yearly",year:n(v)}),class:"block w-full bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-lg text-center transition-colors duration-200"},{default:i(()=>t[16]||(t[16]=[x(" Current Year Analysis ",-1)])),_:1,__:[16]},8,["href"])])])]),e("div",U,[e("div",q,[t[18]||(t[18]=e("h3",{class:"text-lg font-semibold text-white mb-4"},"Top Services by Revenue",-1)),e("div",J,[(g(!0),d(p,null,f(s.topServices,l=>(g(),d("div",{key:l.name,class:"flex items-center justify-between p-3 bg-gray-700 rounded-lg"},[e("div",null,[e("p",K,r(l.name),1),e("p",Q,r(l.orders_count)+" orders",1)]),e("div",W,[e("p",X,r(u(l.total_revenue)),1)])]))),128)),s.topServices.length===0?(g(),d("div",Z," No service data available ")):b("",!0)])]),e("div",ee,[t[19]||(t[19]=e("h3",{class:"text-lg font-semibold text-white mb-4"},"Top Customers by Spending",-1)),e("div",te,[(g(!0),d(p,null,f(s.topCustomers,l=>(g(),d("div",{key:l.id,class:"flex items-center justify-between p-3 bg-gray-700 rounded-lg"},[e("div",null,[e("p",se,r(l.first_name)+" "+r(l.last_name),1),e("p",re,r(l.orders_count)+" orders",1)]),e("div",oe,[e("p",le,r(u(l.total_spent)),1)])]))),128)),s.topCustomers.length===0?(g(),d("div",ne," No customer data available ")):b("",!0)])])])])])]),_:1})],64))}};export{ue as default};
