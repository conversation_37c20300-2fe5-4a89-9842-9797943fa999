<script setup>
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import { Head, Link } from '@inertiajs/vue3';
import { ref, computed } from 'vue';

const props = defineProps({
    partsData: {
        type: Object,
        default: () => ({
            parts_usage: [],
            total_parts_revenue: 0,
            total_parts_profit: 0,
        })
    },
    inventoryMetrics: {
        type: Object,
        default: () => ({
            total_parts: 0,
            low_stock_parts: 0,
            out_of_stock_parts: 0,
            total_inventory_cost: 0,
            total_inventory_value: 0,
            potential_profit: 0,
        })
    },
    profitabilityAnalysis: {
        type: Array,
        default: () => []
    },
    stockAnalysis: {
        type: Array,
        default: () => []
    },
    filters: {
        type: Object,
        default: () => ({})
    },
    dateRange: {
        type: Object,
        default: () => ({
            start: new Date().toISOString(),
            end: new Date().toISOString()
        })
    },
});

const formatCurrency = (amount) => {
    return '₱' + parseFloat(amount || 0).toLocaleString('en-US', { minimumFractionDigits: 2 });
};

const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    });
};

const formatPercentage = (value) => {
    return `${value.toFixed(1)}%`;
};

// Calculate inventory health indicators with null guards
const inventoryHealth = computed(() => {
    const total = props.inventoryMetrics?.total_parts || 0;
    const lowStockParts = props.inventoryMetrics?.low_stock_parts || 0;
    const outOfStockParts = props.inventoryMetrics?.out_of_stock_parts || 0;
    const potentialProfit = props.inventoryMetrics?.potential_profit || 0;

    const lowStockPercentage = total > 0 ? (lowStockParts / total) * 100 : 0;
    const outOfStockPercentage = total > 0 ? (outOfStockParts / total) * 100 : 0;

    let healthScore = 100;
    if (lowStockPercentage > 20) healthScore -= 30;
    if (outOfStockPercentage > 5) healthScore -= 40;
    if (potentialProfit < 0) healthScore -= 20;

    return {
        score: Math.max(0, healthScore),
        lowStockPercentage,
        outOfStockPercentage,
        status: healthScore >= 80 ? 'Excellent' : healthScore >= 60 ? 'Good' : healthScore >= 40 ? 'Fair' : 'Poor'
    };
});

// Top performing parts with null guards
const topParts = computed(() => {
    return props.partsData?.parts_usage?.slice(0, 10) || [];
});
</script>

<template>
    <Head title="Parts Analytics" />

    <AuthenticatedLayout>
        <template #header>
            <div class="flex items-center justify-between">
                <div>
                    <h2 class="text-xl font-semibold leading-tight text-white">
                        Parts & Inventory Analytics
                    </h2>
                    <p class="text-sm text-gray-400 mt-1">
                        {{ formatDate(dateRange?.start) }} - {{ formatDate(dateRange?.end) }}
                    </p>
                </div>
                <div class="flex space-x-3">
                    <Link :href="route('reports.index')" 
                          class="bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors duration-200">
                        Back to Dashboard
                    </Link>
                </div>
            </div>
        </template>

        <div class="py-12">
            <div class="max-w-7xl mx-auto sm:px-6 lg:px-8 space-y-8">
                
                <!-- Key Inventory Metrics -->
                <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                    <div class="bg-gradient-to-br from-blue-900 to-blue-800 border border-blue-700 rounded-xl p-6 shadow-xl">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm text-blue-200">Total Parts</p>
                                <p class="text-3xl font-bold text-white">{{ (inventoryMetrics?.total_parts || 0).toLocaleString() }}</p>
                            </div>
                            <div class="p-3 bg-blue-800 rounded-lg">
                                <svg class="w-8 h-8 text-blue-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                                </svg>
                            </div>
                        </div>
                    </div>

                    <div class="bg-gradient-to-br from-green-900 to-green-800 border border-green-700 rounded-xl p-6 shadow-xl">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm text-green-200">Inventory Value</p>
                                <p class="text-3xl font-bold text-white">{{ formatCurrency(inventoryMetrics?.total_inventory_value || 0) }}</p>
                            </div>
                            <div class="p-3 bg-green-800 rounded-lg">
                                <svg class="w-8 h-8 text-green-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                                </svg>
                            </div>
                        </div>
                    </div>

                    <div class="bg-gradient-to-br from-yellow-900 to-yellow-800 border border-yellow-700 rounded-xl p-6 shadow-xl">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm text-yellow-200">Low Stock Items</p>
                                <p class="text-3xl font-bold text-white">{{ inventoryMetrics?.low_stock_parts || 0 }}</p>
                            </div>
                            <div class="p-3 bg-yellow-800 rounded-lg">
                                <svg class="w-8 h-8 text-yellow-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                                </svg>
                            </div>
                        </div>
                    </div>

                    <div class="bg-gradient-to-br from-purple-900 to-purple-800 border border-purple-700 rounded-xl p-6 shadow-xl">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm text-purple-200">Potential Profit</p>
                                <p class="text-3xl font-bold text-white">{{ formatCurrency(inventoryMetrics?.potential_profit || 0) }}</p>
                            </div>
                            <div class="p-3 bg-purple-800 rounded-lg">
                                <svg class="w-8 h-8 text-purple-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                                </svg>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Inventory Health Score -->
                <div class="bg-gray-800 rounded-xl p-6 border border-gray-700 shadow-xl">
                    <h3 class="text-lg font-semibold text-white mb-6">Inventory Health Score</h3>
                    <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
                        <div class="lg:col-span-1">
                            <div class="text-center">
                                <div class="relative w-32 h-32 mx-auto mb-4">
                                    <svg class="w-32 h-32 transform -rotate-90" viewBox="0 0 36 36">
                                        <path class="text-gray-600" stroke="currentColor" stroke-width="3" fill="none"
                                              d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831" />
                                        <path :class="inventoryHealth.score >= 80 ? 'text-green-400' : inventoryHealth.score >= 60 ? 'text-yellow-400' : 'text-red-400'" 
                                              stroke="currentColor" stroke-width="3" fill="none" stroke-linecap="round"
                                              :stroke-dasharray="`${inventoryHealth.score}, 100`"
                                              d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831" />
                                    </svg>
                                    <div class="absolute inset-0 flex items-center justify-center">
                                        <div class="text-center">
                                            <div class="text-2xl font-bold text-white">{{ inventoryHealth.score.toFixed(0) }}</div>
                                            <div class="text-xs text-gray-400">Score</div>
                                        </div>
                                    </div>
                                </div>
                                <p class="text-lg font-semibold" :class="inventoryHealth.score >= 80 ? 'text-green-400' : inventoryHealth.score >= 60 ? 'text-yellow-400' : 'text-red-400'">
                                    {{ inventoryHealth.status }}
                                </p>
                            </div>
                        </div>
                        <div class="lg:col-span-3 space-y-4">
                            <div class="flex justify-between items-center p-4 bg-gray-700 rounded-lg">
                                <span class="text-white">Out of Stock Items</span>
                                <div class="text-right">
                                    <span class="text-red-400 font-semibold">{{ inventoryMetrics.out_of_stock_parts }}</span>
                                    <span class="text-gray-400 text-sm ml-2">({{ formatPercentage(inventoryHealth.outOfStockPercentage) }})</span>
                                </div>
                            </div>
                            <div class="flex justify-between items-center p-4 bg-gray-700 rounded-lg">
                                <span class="text-white">Low Stock Items</span>
                                <div class="text-right">
                                    <span class="text-yellow-400 font-semibold">{{ inventoryMetrics.low_stock_parts }}</span>
                                    <span class="text-gray-400 text-sm ml-2">({{ formatPercentage(inventoryHealth.lowStockPercentage) }})</span>
                                </div>
                            </div>
                            <div class="flex justify-between items-center p-4 bg-gray-700 rounded-lg">
                                <span class="text-white">Total Inventory Cost</span>
                                <span class="text-white font-semibold">{{ formatCurrency(inventoryMetrics.total_inventory_cost) }}</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Parts Performance & Profitability -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <!-- Top Performing Parts -->
                    <div class="bg-gray-800 rounded-xl p-6 border border-gray-700 shadow-xl">
                        <h3 class="text-lg font-semibold text-white mb-6">Top Performing Parts</h3>
                        <div class="space-y-3">
                            <div v-for="(part, index) in topParts" :key="part.name" 
                                 class="flex items-center justify-between p-3 bg-gray-700 rounded-lg">
                                <div class="flex items-center">
                                    <div class="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-bold text-sm mr-3">
                                        {{ index + 1 }}
                                    </div>
                                    <div>
                                        <p class="text-white font-medium">{{ part.name }}</p>
                                        <p class="text-gray-400 text-sm">{{ part.part_number }} • {{ part.category }}</p>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <p class="text-white font-semibold">{{ formatCurrency(part.total_revenue) }}</p>
                                    <p class="text-gray-400 text-sm">{{ part.total_used }} used</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Profitability Analysis -->
                    <div class="bg-gray-800 rounded-xl p-6 border border-gray-700 shadow-xl">
                        <h3 class="text-lg font-semibold text-white mb-6">Profitability by Category</h3>
                        <div class="space-y-4">
                            <div v-for="category in profitabilityAnalysis" :key="category.category" 
                                 class="p-4 bg-gray-700 rounded-lg">
                                <div class="flex justify-between items-center mb-2">
                                    <h4 class="text-white font-medium">{{ category.category }}</h4>
                                    <span class="text-green-400 font-semibold">{{ formatPercentage(category.margin) }}</span>
                                </div>
                                <div class="grid grid-cols-3 gap-4 text-sm">
                                    <div>
                                        <p class="text-gray-400">Revenue</p>
                                        <p class="text-white font-medium">{{ formatCurrency(category.revenue) }}</p>
                                    </div>
                                    <div>
                                        <p class="text-gray-400">Cost</p>
                                        <p class="text-white font-medium">{{ formatCurrency(category.cost) }}</p>
                                    </div>
                                    <div>
                                        <p class="text-gray-400">Profit</p>
                                        <p class="text-green-400 font-medium">{{ formatCurrency(category.profit) }}</p>
                                    </div>
                                </div>
                                <div class="mt-2 bg-gray-600 rounded-full h-2">
                                    <div class="bg-green-500 h-2 rounded-full" 
                                         :style="{ width: Math.min(100, category.margin) + '%' }"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Stock Analysis by Category -->
                <div class="bg-gray-800 rounded-xl p-6 border border-gray-700 shadow-xl">
                    <h3 class="text-lg font-semibold text-white mb-6">Stock Analysis by Category</h3>
                    <div class="overflow-x-auto">
                        <table class="w-full text-sm">
                            <thead>
                                <tr class="border-b border-gray-700">
                                    <th class="text-left text-gray-400 font-medium py-3">Category</th>
                                    <th class="text-right text-gray-400 font-medium py-3">Total Parts</th>
                                    <th class="text-right text-gray-400 font-medium py-3">Total Quantity</th>
                                    <th class="text-right text-gray-400 font-medium py-3">Value</th>
                                    <th class="text-right text-gray-400 font-medium py-3">Out of Stock</th>
                                    <th class="text-right text-gray-400 font-medium py-3">Low Stock</th>
                                    <th class="text-right text-gray-400 font-medium py-3">Avg Margin</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr v-for="category in stockAnalysis" :key="category.category" 
                                    class="border-b border-gray-700 hover:bg-gray-700/50">
                                    <td class="py-3 text-white font-medium">{{ category.category }}</td>
                                    <td class="py-3 text-right text-white">{{ category.total_parts }}</td>
                                    <td class="py-3 text-right text-white">{{ category.total_quantity.toLocaleString() }}</td>
                                    <td class="py-3 text-right text-white">{{ formatCurrency(category.category_value) }}</td>
                                    <td class="py-3 text-right">
                                        <span :class="category.out_of_stock > 0 ? 'text-red-400' : 'text-gray-400'">
                                            {{ category.out_of_stock }}
                                        </span>
                                    </td>
                                    <td class="py-3 text-right">
                                        <span :class="category.low_stock > 0 ? 'text-yellow-400' : 'text-gray-400'">
                                            {{ category.low_stock }}
                                        </span>
                                    </td>
                                    <td class="py-3 text-right text-green-400">{{ formatPercentage(category.avg_margin_percentage) }}</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Summary Cards -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div class="bg-gray-800 rounded-xl p-6 border border-gray-700 shadow-xl text-center">
                        <h4 class="text-lg font-semibold text-white mb-2">Total Parts Revenue</h4>
                        <p class="text-3xl font-bold text-green-400">{{ formatCurrency(partsData.total_parts_revenue) }}</p>
                        <p class="text-gray-400 text-sm mt-2">For selected period</p>
                    </div>
                    
                    <div class="bg-gray-800 rounded-xl p-6 border border-gray-700 shadow-xl text-center">
                        <h4 class="text-lg font-semibold text-white mb-2">Total Parts Profit</h4>
                        <p class="text-3xl font-bold text-purple-400">{{ formatCurrency(partsData.total_parts_profit) }}</p>
                        <p class="text-gray-400 text-sm mt-2">Gross profit margin</p>
                    </div>
                    
                    <div class="bg-gray-800 rounded-xl p-6 border border-gray-700 shadow-xl text-center">
                        <h4 class="text-lg font-semibold text-white mb-2">Average Margin</h4>
                        <p class="text-3xl font-bold text-blue-400">
                            {{ partsData.total_parts_revenue > 0 ? formatPercentage((partsData.total_parts_profit / partsData.total_parts_revenue) * 100) : '0%' }}
                        </p>
                        <p class="text-gray-400 text-sm mt-2">Overall profitability</p>
                    </div>
                </div>

            </div>
        </div>
    </AuthenticatedLayout>
</template>

<style scoped>
/* Custom styles for parts analytics */
</style>
