/* Sidebar Performance Optimizations */

/* Hardware acceleration for smooth animations */
.sidebar-optimized {
    transform: translateZ(0);
    backface-visibility: hidden;
    perspective: 1000px;
    will-change: transform, width;
}

/* Smooth transitions with GPU acceleration */
.sidebar-transition {
    transition: transform 300ms cubic-bezier(0.4, 0, 0.2, 1),
                width 300ms cubic-bezier(0.4, 0, 0.2, 1);
}

/* Optimized navigation items */
.nav-item-optimized {
    transform: translateZ(0);
    will-change: background-color, color;
    transition: background-color 200ms ease-out, color 200ms ease-out;
}

/* Prevent layout thrashing */
.nav-item-optimized:hover {
    transform: translateZ(0);
}

/* Smooth text transitions */
.nav-text-transition {
    transition: opacity 200ms ease-out;
    will-change: opacity;
}

/* Mobile overlay optimization */
.mobile-overlay {
    backdrop-filter: blur(4px);
    -webkit-backdrop-filter: blur(4px);
}

/* Collapse button optimization */
.collapse-btn {
    transform: translateZ(0);
    transition: transform 200ms ease-out, background-color 200ms ease-out;
}

.collapse-btn:hover {
    transform: translateZ(0) scale(1.05);
}

/* Main content optimization */
.main-content-optimized {
    transform: translateZ(0);
    will-change: margin-left;
    transition: margin-left 300ms cubic-bezier(0.4, 0, 0.2, 1);
}

/* Tooltip optimization */
.tooltip-optimized {
    transform: translateZ(0);
    will-change: opacity;
    transition: opacity 200ms ease-out;
    pointer-events: none;
}

/* Reduce paint operations */
.reduce-paint {
    contain: layout style paint;
}

/* Optimize for 60fps animations */
@media (prefers-reduced-motion: no-preference) {
    .sidebar-optimized,
    .nav-item-optimized,
    .main-content-optimized {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 300ms !important;
    }
}

/* Respect user's motion preferences */
@media (prefers-reduced-motion: reduce) {
    .sidebar-optimized,
    .nav-item-optimized,
    .main-content-optimized,
    .nav-text-transition,
    .collapse-btn {
        transition: none !important;
        animation: none !important;
    }
}

/* Force hardware acceleration on mobile */
@media (max-width: 1023px) {
    .sidebar-optimized {
        transform: translate3d(0, 0, 0);
        -webkit-transform: translate3d(0, 0, 0);
    }
}

/* Optimize scrolling performance */
.sidebar-scroll {
    -webkit-overflow-scrolling: touch;
    overflow-scrolling: touch;
    scroll-behavior: smooth;
}

/* Prevent unnecessary repaints */
.prevent-repaint {
    transform: translateZ(0);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}
