import{_ as P}from"./AuthenticatedLayout-D449e8ZD.js";import{_ as M,m as w,g as i,o as d,a as y,d as k,h as j,w as _,b as t,t as s,n as g,F as p,y as h,J as D,l as C,f as A}from"./app-wnQ52fJE.js";import"./ApplicationLogo-BIrQV_G-.js";import"./DropdownLink-BLptVuux.js";const O={class:"flex items-center justify-between"},L={class:"text-sm text-gray-400 mt-1"},I={class:"flex space-x-3"},B={class:"py-12"},F={class:"max-w-7xl mx-auto sm:px-6 lg:px-8 space-y-8"},T={class:"grid grid-cols-1 md:grid-cols-4 gap-6"},V={class:"bg-gradient-to-br from-blue-900 to-blue-800 border border-blue-700 rounded-xl p-6 shadow-xl"},z={class:"flex items-center justify-between"},R={class:"text-3xl font-bold text-white"},H={class:"bg-gradient-to-br from-green-900 to-green-800 border border-green-700 rounded-xl p-6 shadow-xl"},N={class:"flex items-center justify-between"},$={class:"text-3xl font-bold text-white"},E={class:"bg-gradient-to-br from-yellow-900 to-yellow-800 border border-yellow-700 rounded-xl p-6 shadow-xl"},G={class:"flex items-center justify-between"},U={class:"text-3xl font-bold text-white"},q={class:"bg-gradient-to-br from-purple-900 to-purple-800 border border-purple-700 rounded-xl p-6 shadow-xl"},J={class:"flex items-center justify-between"},Q={class:"text-3xl font-bold text-white"},K={class:"bg-gray-800 rounded-xl p-6 border border-gray-700 shadow-xl"},W={class:"grid grid-cols-1 lg:grid-cols-4 gap-6"},X={class:"lg:col-span-1"},Y={class:"text-center"},Z={class:"relative w-32 h-32 mx-auto mb-4"},tt={class:"w-32 h-32 transform -rotate-90",viewBox:"0 0 36 36"},et=["stroke-dasharray"],st={class:"absolute inset-0 flex items-center justify-center"},ot={class:"text-center"},lt={class:"text-2xl font-bold text-white"},rt={class:"lg:col-span-3 space-y-4"},at={class:"flex justify-between items-center p-4 bg-gray-700 rounded-lg"},nt={class:"text-right"},it={class:"text-red-400 font-semibold"},dt={class:"text-gray-400 text-sm ml-2"},ct={class:"flex justify-between items-center p-4 bg-gray-700 rounded-lg"},xt={class:"text-right"},ut={class:"text-yellow-400 font-semibold"},gt={class:"text-gray-400 text-sm ml-2"},pt={class:"flex justify-between items-center p-4 bg-gray-700 rounded-lg"},mt={class:"text-white font-semibold"},yt={class:"grid grid-cols-1 lg:grid-cols-2 gap-6"},_t={class:"bg-gray-800 rounded-xl p-6 border border-gray-700 shadow-xl"},ht={class:"space-y-3"},ft={class:"flex items-center"},vt={class:"w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-bold text-sm mr-3"},bt={class:"text-white font-medium"},wt={class:"text-gray-400 text-sm"},kt={class:"text-right"},St={class:"text-white font-semibold"},Pt={class:"text-gray-400 text-sm"},Mt={class:"bg-gray-800 rounded-xl p-6 border border-gray-700 shadow-xl"},jt={class:"space-y-4"},Dt={class:"flex justify-between items-center mb-2"},Ct={class:"text-white font-medium"},At={class:"text-green-400 font-semibold"},Ot={class:"grid grid-cols-3 gap-4 text-sm"},Lt={class:"text-white font-medium"},It={class:"text-white font-medium"},Bt={class:"text-green-400 font-medium"},Ft={class:"mt-2 bg-gray-600 rounded-full h-2"},Tt={class:"bg-gray-800 rounded-xl p-6 border border-gray-700 shadow-xl"},Vt={class:"overflow-x-auto"},zt={class:"w-full text-sm"},Rt={class:"py-3 text-white font-medium"},Ht={class:"py-3 text-right text-white"},Nt={class:"py-3 text-right text-white"},$t={class:"py-3 text-right text-white"},Et={class:"py-3 text-right"},Gt={class:"py-3 text-right"},Ut={class:"py-3 text-right text-green-400"},qt={class:"grid grid-cols-1 md:grid-cols-3 gap-6"},Jt={class:"bg-gray-800 rounded-xl p-6 border border-gray-700 shadow-xl text-center"},Qt={class:"text-3xl font-bold text-green-400"},Kt={class:"bg-gray-800 rounded-xl p-6 border border-gray-700 shadow-xl text-center"},Wt={class:"text-3xl font-bold text-purple-400"},Xt={class:"bg-gray-800 rounded-xl p-6 border border-gray-700 shadow-xl text-center"},Yt={class:"text-3xl font-bold text-blue-400"},Zt={__name:"PartsAnalytics",props:{partsData:{type:Object,default:()=>({parts_usage:[],total_parts_revenue:0,total_parts_profit:0})},inventoryMetrics:{type:Object,default:()=>({total_parts:0,low_stock_parts:0,out_of_stock_parts:0,total_inventory_cost:0,total_inventory_value:0,potential_profit:0})},profitabilityAnalysis:{type:Array,default:()=>[]},stockAnalysis:{type:Array,default:()=>[]},filters:{type:Object,default:()=>({})},dateRange:{type:Object,default:()=>({start:new Date().toISOString(),end:new Date().toISOString()})}},setup(l){const x=l,a=r=>"₱"+parseFloat(r||0).toLocaleString("en-US",{minimumFractionDigits:2}),f=r=>new Date(r).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"}),u=r=>`${r.toFixed(1)}%`,n=w(()=>{const r=x.inventoryMetrics?.total_parts||0,e=x.inventoryMetrics?.low_stock_parts||0,o=x.inventoryMetrics?.out_of_stock_parts||0,m=x.inventoryMetrics?.potential_profit||0,v=r>0?e/r*100:0,b=r>0?o/r*100:0;let c=100;return v>20&&(c-=30),b>5&&(c-=40),m<0&&(c-=20),{score:Math.max(0,c),lowStockPercentage:v,outOfStockPercentage:b,status:c>=80?"Excellent":c>=60?"Good":c>=40?"Fair":"Poor"}}),S=w(()=>x.partsData?.parts_usage?.slice(0,10)||[]);return(r,e)=>(d(),i(p,null,[y(k(j),{title:"Parts Analytics"}),y(P,null,{header:_(()=>[t("div",O,[t("div",null,[e[0]||(e[0]=t("h2",{class:"text-xl font-semibold leading-tight text-white"}," Parts & Inventory Analytics ",-1)),t("p",L,s(f(l.dateRange?.start))+" - "+s(f(l.dateRange?.end)),1)]),t("div",I,[y(k(C),{href:r.route("reports.index"),class:"bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors duration-200"},{default:_(()=>e[1]||(e[1]=[A(" Back to Dashboard ",-1)])),_:1,__:[1]},8,["href"])])])]),default:_(()=>[t("div",B,[t("div",F,[t("div",T,[t("div",V,[t("div",z,[t("div",null,[e[2]||(e[2]=t("p",{class:"text-sm text-blue-200"},"Total Parts",-1)),t("p",R,s((l.inventoryMetrics?.total_parts||0).toLocaleString()),1)]),e[3]||(e[3]=t("div",{class:"p-3 bg-blue-800 rounded-lg"},[t("svg",{class:"w-8 h-8 text-blue-200",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"})])],-1))])]),t("div",H,[t("div",N,[t("div",null,[e[4]||(e[4]=t("p",{class:"text-sm text-green-200"},"Inventory Value",-1)),t("p",$,s(a(l.inventoryMetrics?.total_inventory_value||0)),1)]),e[5]||(e[5]=t("div",{class:"p-3 bg-green-800 rounded-lg"},[t("svg",{class:"w-8 h-8 text-green-200",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})])],-1))])]),t("div",E,[t("div",G,[t("div",null,[e[6]||(e[6]=t("p",{class:"text-sm text-yellow-200"},"Low Stock Items",-1)),t("p",U,s(l.inventoryMetrics?.low_stock_parts||0),1)]),e[7]||(e[7]=t("div",{class:"p-3 bg-yellow-800 rounded-lg"},[t("svg",{class:"w-8 h-8 text-yellow-200",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})])],-1))])]),t("div",q,[t("div",J,[t("div",null,[e[8]||(e[8]=t("p",{class:"text-sm text-purple-200"},"Potential Profit",-1)),t("p",Q,s(a(l.inventoryMetrics?.potential_profit||0)),1)]),e[9]||(e[9]=t("div",{class:"p-3 bg-purple-800 rounded-lg"},[t("svg",{class:"w-8 h-8 text-purple-200",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"})])],-1))])])]),t("div",K,[e[15]||(e[15]=t("h3",{class:"text-lg font-semibold text-white mb-6"},"Inventory Health Score",-1)),t("div",W,[t("div",X,[t("div",Y,[t("div",Z,[(d(),i("svg",tt,[e[10]||(e[10]=t("path",{class:"text-gray-600",stroke:"currentColor","stroke-width":"3",fill:"none",d:"M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"},null,-1)),t("path",{class:g(n.value.score>=80?"text-green-400":n.value.score>=60?"text-yellow-400":"text-red-400"),stroke:"currentColor","stroke-width":"3",fill:"none","stroke-linecap":"round","stroke-dasharray":`${n.value.score}, 100`,d:"M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"},null,10,et)])),t("div",st,[t("div",ot,[t("div",lt,s(n.value.score.toFixed(0)),1),e[11]||(e[11]=t("div",{class:"text-xs text-gray-400"},"Score",-1))])])]),t("p",{class:g(["text-lg font-semibold",n.value.score>=80?"text-green-400":n.value.score>=60?"text-yellow-400":"text-red-400"])},s(n.value.status),3)])]),t("div",rt,[t("div",at,[e[12]||(e[12]=t("span",{class:"text-white"},"Out of Stock Items",-1)),t("div",nt,[t("span",it,s(l.inventoryMetrics.out_of_stock_parts),1),t("span",dt,"("+s(u(n.value.outOfStockPercentage))+")",1)])]),t("div",ct,[e[13]||(e[13]=t("span",{class:"text-white"},"Low Stock Items",-1)),t("div",xt,[t("span",ut,s(l.inventoryMetrics.low_stock_parts),1),t("span",gt,"("+s(u(n.value.lowStockPercentage))+")",1)])]),t("div",pt,[e[14]||(e[14]=t("span",{class:"text-white"},"Total Inventory Cost",-1)),t("span",mt,s(a(l.inventoryMetrics.total_inventory_cost)),1)])])])]),t("div",yt,[t("div",_t,[e[16]||(e[16]=t("h3",{class:"text-lg font-semibold text-white mb-6"},"Top Performing Parts",-1)),t("div",ht,[(d(!0),i(p,null,h(S.value,(o,m)=>(d(),i("div",{key:o.name,class:"flex items-center justify-between p-3 bg-gray-700 rounded-lg"},[t("div",ft,[t("div",vt,s(m+1),1),t("div",null,[t("p",bt,s(o.name),1),t("p",wt,s(o.part_number)+" • "+s(o.category),1)])]),t("div",kt,[t("p",St,s(a(o.total_revenue)),1),t("p",Pt,s(o.total_used)+" used",1)])]))),128))])]),t("div",Mt,[e[20]||(e[20]=t("h3",{class:"text-lg font-semibold text-white mb-6"},"Profitability by Category",-1)),t("div",jt,[(d(!0),i(p,null,h(l.profitabilityAnalysis,o=>(d(),i("div",{key:o.category,class:"p-4 bg-gray-700 rounded-lg"},[t("div",Dt,[t("h4",Ct,s(o.category),1),t("span",At,s(u(o.margin)),1)]),t("div",Ot,[t("div",null,[e[17]||(e[17]=t("p",{class:"text-gray-400"},"Revenue",-1)),t("p",Lt,s(a(o.revenue)),1)]),t("div",null,[e[18]||(e[18]=t("p",{class:"text-gray-400"},"Cost",-1)),t("p",It,s(a(o.cost)),1)]),t("div",null,[e[19]||(e[19]=t("p",{class:"text-gray-400"},"Profit",-1)),t("p",Bt,s(a(o.profit)),1)])]),t("div",Ft,[t("div",{class:"bg-green-500 h-2 rounded-full",style:D({width:Math.min(100,o.margin)+"%"})},null,4)])]))),128))])])]),t("div",Tt,[e[22]||(e[22]=t("h3",{class:"text-lg font-semibold text-white mb-6"},"Stock Analysis by Category",-1)),t("div",Vt,[t("table",zt,[e[21]||(e[21]=t("thead",null,[t("tr",{class:"border-b border-gray-700"},[t("th",{class:"text-left text-gray-400 font-medium py-3"},"Category"),t("th",{class:"text-right text-gray-400 font-medium py-3"},"Total Parts"),t("th",{class:"text-right text-gray-400 font-medium py-3"},"Total Quantity"),t("th",{class:"text-right text-gray-400 font-medium py-3"},"Value"),t("th",{class:"text-right text-gray-400 font-medium py-3"},"Out of Stock"),t("th",{class:"text-right text-gray-400 font-medium py-3"},"Low Stock"),t("th",{class:"text-right text-gray-400 font-medium py-3"},"Avg Margin")])],-1)),t("tbody",null,[(d(!0),i(p,null,h(l.stockAnalysis,o=>(d(),i("tr",{key:o.category,class:"border-b border-gray-700 hover:bg-gray-700/50"},[t("td",Rt,s(o.category),1),t("td",Ht,s(o.total_parts),1),t("td",Nt,s(o.total_quantity.toLocaleString()),1),t("td",$t,s(a(o.category_value)),1),t("td",Et,[t("span",{class:g(o.out_of_stock>0?"text-red-400":"text-gray-400")},s(o.out_of_stock),3)]),t("td",Gt,[t("span",{class:g(o.low_stock>0?"text-yellow-400":"text-gray-400")},s(o.low_stock),3)]),t("td",Ut,s(u(o.avg_margin_percentage)),1)]))),128))])])])]),t("div",qt,[t("div",Jt,[e[23]||(e[23]=t("h4",{class:"text-lg font-semibold text-white mb-2"},"Total Parts Revenue",-1)),t("p",Qt,s(a(l.partsData.total_parts_revenue)),1),e[24]||(e[24]=t("p",{class:"text-gray-400 text-sm mt-2"},"For selected period",-1))]),t("div",Kt,[e[25]||(e[25]=t("h4",{class:"text-lg font-semibold text-white mb-2"},"Total Parts Profit",-1)),t("p",Wt,s(a(l.partsData.total_parts_profit)),1),e[26]||(e[26]=t("p",{class:"text-gray-400 text-sm mt-2"},"Gross profit margin",-1))]),t("div",Xt,[e[27]||(e[27]=t("h4",{class:"text-lg font-semibold text-white mb-2"},"Average Margin",-1)),t("p",Yt,s(l.partsData.total_parts_revenue>0?u(l.partsData.total_parts_profit/l.partsData.total_parts_revenue*100):"0%"),1),e[28]||(e[28]=t("p",{class:"text-gray-400 text-sm mt-2"},"Overall profitability",-1))])])])])]),_:1})],64))}},le=M(Zt,[["__scopeId","data-v-98049500"]]);export{le as default};
