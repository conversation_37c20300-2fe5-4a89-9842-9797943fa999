<script setup>
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import { Head, Link } from '@inertiajs/vue3';
import { ref, computed } from 'vue';

const props = defineProps({
    serviceData: {
        type: Object,
        default: () => ({
            service_data: [],
            total_service_revenue: 0,
            total_service_orders: 0,
        })
    },
    performanceMetrics: {
        type: Array,
        default: () => []
    },
    demandAnalysis: {
        type: Array,
        default: () => []
    },
    profitabilityAnalysis: {
        type: Array,
        default: () => []
    },
    filters: {
        type: Object,
        default: () => ({})
    },
    dateRange: {
        type: Object,
        default: () => ({
            start: new Date().toISOString(),
            end: new Date().toISOString()
        })
    },
});

const formatCurrency = (amount) => {
    return '₱' + parseFloat(amount || 0).toLocaleString('en-US', { minimumFractionDigits: 2 });
};

const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    });
};

const formatHours = (minutes) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return hours > 0 ? `${hours}h ${mins}m` : `${mins}m`;
};

// Top performing services with null guards
const topServices = computed(() => {
    return props.serviceData?.service_data?.slice(0, 10) || [];
});

// Service efficiency analysis with null guards
const serviceEfficiency = computed(() => {
    const metrics = props.performanceMetrics || [];
    return metrics.map(service => ({
        ...service,
        efficiency: (service?.estimated_hours || 0) > 0 ?
            (((service?.estimated_hours || 0) / (service?.avg_completion_hours || 1)) * 100).toFixed(1) : 0,
        variance: (service?.estimated_hours || 0) > 0 ?
            ((service?.avg_completion_hours || 0) - (service?.estimated_hours || 0)).toFixed(1) : 0
    }));
});
</script>

<template>
    <Head title="Service Analytics" />

    <AuthenticatedLayout>
        <template #header>
            <div class="flex items-center justify-between">
                <div>
                    <h2 class="text-xl font-semibold leading-tight text-white">
                        Service Performance Analytics
                    </h2>
                    <p class="text-sm text-gray-400 mt-1">
                        {{ formatDate(dateRange.start) }} - {{ formatDate(dateRange.end) }}
                    </p>
                </div>
                <div class="flex space-x-3">
                    <Link :href="route('reports.index')" 
                          class="bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors duration-200">
                        Back to Dashboard
                    </Link>
                </div>
            </div>
        </template>

        <div class="py-12">
            <div class="max-w-7xl mx-auto sm:px-6 lg:px-8 space-y-8">
                
                <!-- Key Service Metrics -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div class="bg-gradient-to-br from-green-900 to-green-800 border border-green-700 rounded-xl p-6 shadow-xl">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm text-green-200">Total Service Revenue</p>
                                <p class="text-3xl font-bold text-white">{{ formatCurrency(serviceData.total_service_revenue) }}</p>
                            </div>
                            <div class="p-3 bg-green-800 rounded-lg">
                                <svg class="w-8 h-8 text-green-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                                </svg>
                            </div>
                        </div>
                    </div>

                    <div class="bg-gradient-to-br from-blue-900 to-blue-800 border border-blue-700 rounded-xl p-6 shadow-xl">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm text-blue-200">Total Service Orders</p>
                                <p class="text-3xl font-bold text-white">{{ serviceData.total_service_orders?.toLocaleString() || 0 }}</p>
                            </div>
                            <div class="p-3 bg-blue-800 rounded-lg">
                                <svg class="w-8 h-8 text-blue-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                </svg>
                            </div>
                        </div>
                    </div>

                    <div class="bg-gradient-to-br from-purple-900 to-purple-800 border border-purple-700 rounded-xl p-6 shadow-xl">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm text-purple-200">Average Service Value</p>
                                <p class="text-3xl font-bold text-white">
                                    {{ serviceData.total_service_orders > 0 ? formatCurrency(serviceData.total_service_revenue / serviceData.total_service_orders) : formatCurrency(0) }}
                                </p>
                            </div>
                            <div class="p-3 bg-purple-800 rounded-lg">
                                <svg class="w-8 h-8 text-purple-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                                </svg>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Top Performing Services -->
                <div class="bg-gray-800 rounded-xl p-6 border border-gray-700 shadow-xl">
                    <h3 class="text-lg font-semibold text-white mb-6">Top Performing Services</h3>
                    <div class="overflow-x-auto">
                        <table class="w-full text-sm">
                            <thead>
                                <tr class="border-b border-gray-700">
                                    <th class="text-left text-gray-400 font-medium py-3">Service</th>
                                    <th class="text-right text-gray-400 font-medium py-3">Orders</th>
                                    <th class="text-right text-gray-400 font-medium py-3">Revenue</th>
                                    <th class="text-right text-gray-400 font-medium py-3">Avg Price</th>
                                    <th class="text-right text-gray-400 font-medium py-3">Base Price</th>
                                    <th class="text-right text-gray-400 font-medium py-3">Est. Duration</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr v-for="(service, index) in topServices" :key="service.name" 
                                    class="border-b border-gray-700 hover:bg-gray-700/50">
                                    <td class="py-3">
                                        <div class="flex items-center">
                                            <div class="w-6 h-6 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-bold text-xs mr-3">
                                                {{ index + 1 }}
                                            </div>
                                            <span class="text-white font-medium">{{ service.name }}</span>
                                        </div>
                                    </td>
                                    <td class="py-3 text-right text-white">{{ service.total_orders }}</td>
                                    <td class="py-3 text-right text-green-400 font-semibold">{{ formatCurrency(service.total_revenue) }}</td>
                                    <td class="py-3 text-right text-white">{{ formatCurrency(service.avg_price) }}</td>
                                    <td class="py-3 text-right text-gray-400">{{ formatCurrency(service.base_price) }}</td>
                                    <td class="py-3 text-right text-gray-400">{{ formatHours(service.estimated_duration) }}</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Service Performance & Efficiency -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <!-- Performance Metrics -->
                    <div class="bg-gray-800 rounded-xl p-6 border border-gray-700 shadow-xl">
                        <h3 class="text-lg font-semibold text-white mb-6">Service Efficiency Analysis</h3>
                        <div class="space-y-4">
                            <div v-for="service in serviceEfficiency.slice(0, 8)" :key="service.name" 
                                 class="p-4 bg-gray-700 rounded-lg">
                                <div class="flex justify-between items-center mb-2">
                                    <h4 class="text-white font-medium">{{ service.name }}</h4>
                                    <span :class="service.efficiency >= 100 ? 'text-green-400' : service.efficiency >= 80 ? 'text-yellow-400' : 'text-red-400'" 
                                          class="font-semibold">
                                        {{ service.efficiency }}% efficient
                                    </span>
                                </div>
                                <div class="grid grid-cols-2 gap-4 text-sm">
                                    <div>
                                        <p class="text-gray-400">Estimated</p>
                                        <p class="text-white">{{ service.estimated_hours }}h</p>
                                    </div>
                                    <div>
                                        <p class="text-gray-400">Actual Avg</p>
                                        <p class="text-white">{{ service.avg_completion_hours }}h</p>
                                    </div>
                                </div>
                                <div class="mt-2 bg-gray-600 rounded-full h-2">
                                    <div :class="service.efficiency >= 100 ? 'bg-green-500' : service.efficiency >= 80 ? 'bg-yellow-500' : 'bg-red-500'" 
                                         class="h-2 rounded-full" 
                                         :style="{ width: Math.min(100, service.efficiency) + '%' }"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Service Profitability -->
                    <div class="bg-gray-800 rounded-xl p-6 border border-gray-700 shadow-xl">
                        <h3 class="text-lg font-semibold text-white mb-6">Service Profitability</h3>
                        <div class="space-y-4">
                            <div v-for="service in profitabilityAnalysis.slice(0, 8)" :key="service.name" 
                                 class="p-4 bg-gray-700 rounded-lg">
                                <div class="flex justify-between items-center mb-2">
                                    <h4 class="text-white font-medium">{{ service.name }}</h4>
                                    <span class="text-green-400 font-semibold">{{ formatCurrency(service.net_profit) }}</span>
                                </div>
                                <div class="grid grid-cols-3 gap-2 text-sm">
                                    <div>
                                        <p class="text-gray-400">Revenue</p>
                                        <p class="text-white">{{ formatCurrency(service.service_revenue) }}</p>
                                    </div>
                                    <div>
                                        <p class="text-gray-400">Parts Cost</p>
                                        <p class="text-red-400">{{ formatCurrency(service.parts_cost) }}</p>
                                    </div>
                                    <div>
                                        <p class="text-gray-400">Net Profit</p>
                                        <p class="text-green-400">{{ formatCurrency(service.net_profit) }}</p>
                                    </div>
                                </div>
                                <div class="mt-2 bg-gray-600 rounded-full h-2">
                                    <div class="bg-green-500 h-2 rounded-full" 
                                         :style="{ width: service.service_revenue > 0 ? Math.min(100, (service.net_profit / service.service_revenue) * 100) + '%' : '0%' }"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Service Demand Analysis -->
                <div class="bg-gray-800 rounded-xl p-6 border border-gray-700 shadow-xl">
                    <h3 class="text-lg font-semibold text-white mb-6">Service Demand Trends</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        <div v-for="demand in demandAnalysis.slice(0, 12)" :key="`${demand.name}-${demand.month}-${demand.year}`" 
                             class="bg-gray-700 rounded-lg p-4">
                            <div class="flex justify-between items-start mb-2">
                                <h4 class="text-white font-medium text-sm">{{ demand.name }}</h4>
                                <span class="text-xs text-gray-400">{{ demand.month }}/{{ demand.year }}</span>
                            </div>
                            <p class="text-2xl font-bold text-blue-400">{{ demand.demand_count }}</p>
                            <p class="text-gray-400 text-sm">orders this month</p>
                        </div>
                    </div>
                </div>

                <!-- Summary Statistics -->
                <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                    <div class="bg-gray-800 rounded-xl p-6 border border-gray-700 shadow-xl text-center">
                        <h4 class="text-lg font-semibold text-white mb-2">Most Popular Service</h4>
                        <p class="text-xl font-bold text-blue-400">
                            {{ topServices[0]?.name || 'N/A' }}
                        </p>
                        <p class="text-gray-400 text-sm mt-2">
                            {{ topServices[0]?.total_orders || 0 }} orders
                        </p>
                    </div>
                    
                    <div class="bg-gray-800 rounded-xl p-6 border border-gray-700 shadow-xl text-center">
                        <h4 class="text-lg font-semibold text-white mb-2">Highest Revenue Service</h4>
                        <p class="text-xl font-bold text-green-400">
                            {{ topServices[0]?.name || 'N/A' }}
                        </p>
                        <p class="text-gray-400 text-sm mt-2">
                            {{ formatCurrency(topServices[0]?.total_revenue || 0) }}
                        </p>
                    </div>
                    
                    <div class="bg-gray-800 rounded-xl p-6 border border-gray-700 shadow-xl text-center">
                        <h4 class="text-lg font-semibold text-white mb-2">Most Efficient Service</h4>
                        <p class="text-xl font-bold text-purple-400">
                            {{ serviceEfficiency[0]?.name || 'N/A' }}
                        </p>
                        <p class="text-gray-400 text-sm mt-2">
                            {{ serviceEfficiency[0]?.efficiency || 0 }}% efficiency
                        </p>
                    </div>
                    
                    <div class="bg-gray-800 rounded-xl p-6 border border-gray-700 shadow-xl text-center">
                        <h4 class="text-lg font-semibold text-white mb-2">Most Profitable Service</h4>
                        <p class="text-xl font-bold text-yellow-400">
                            {{ profitabilityAnalysis[0]?.name || 'N/A' }}
                        </p>
                        <p class="text-gray-400 text-sm mt-2">
                            {{ formatCurrency(profitabilityAnalysis[0]?.net_profit || 0) }}
                        </p>
                    </div>
                </div>

            </div>
        </div>
    </AuthenticatedLayout>
</template>

<style scoped>
/* Custom styles for service analytics */
</style>
