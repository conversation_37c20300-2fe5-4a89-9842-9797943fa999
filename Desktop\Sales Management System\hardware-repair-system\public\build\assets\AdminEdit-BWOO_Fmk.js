import{_ as d}from"./AuthenticatedLayout-D449e8ZD.js";import a from"./DeleteUserForm-Ce47N0tY.js";import i from"./UpdatePasswordForm-dGDm8t3y.js";import l from"./UpdateProfileInformationForm-sbkI9DxP.js";import{g as n,o as c,a as r,d as m,h as g,w as o,b as e,F as x}from"./app-wnQ52fJE.js";import"./ApplicationLogo-BIrQV_G-.js";import"./DropdownLink-BLptVuux.js";import"./TextInput-jgbPOCPR.js";import"./PrimaryButton-BTRdXBgS.js";const f={class:"p-6"},u={class:"max-w-4xl mx-auto space-y-6"},p={class:"bg-gradient-to-br from-gray-900 to-gray-800 border border-gray-700 rounded-xl shadow-xl overflow-hidden"},b={class:"p-6"},h={class:"bg-gradient-to-br from-gray-900 to-gray-800 border border-gray-700 rounded-xl shadow-xl overflow-hidden"},v={class:"p-6"},w={class:"bg-gradient-to-br from-red-900 to-red-800 border border-red-700 rounded-xl shadow-xl overflow-hidden"},y={class:"p-6"},E={__name:"AdminEdit",props:{mustVerifyEmail:{type:Boolean},status:{type:String}},setup(s){return(k,t)=>(c(),n(x,null,[r(m(g),{title:"Admin Profile"}),r(d,null,{header:o(()=>t[0]||(t[0]=[e("div",{class:"flex items-center justify-between"},[e("div",{class:"flex items-center space-x-4"},[e("div",{class:"w-12 h-12 bg-gradient-to-br from-red-500 to-red-600 rounded-full flex items-center justify-center shadow-lg"},[e("svg",{class:"w-6 h-6 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})])]),e("div",null,[e("h2",{class:"text-2xl font-bold text-white"},"Admin Profile"),e("p",{class:"text-gray-400 text-sm"},"Manage your administrator account settings")])])],-1)])),default:o(()=>[e("div",f,[e("div",u,[e("div",p,[t[1]||(t[1]=e("div",{class:"px-6 py-4 border-b border-gray-700 bg-gradient-to-r from-gray-800 to-gray-700"},[e("div",{class:"flex items-center space-x-3"},[e("div",{class:"p-2 bg-red-600 rounded-lg"},[e("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})])]),e("h3",{class:"text-lg font-semibold text-white"},"Profile Information")])],-1)),e("div",b,[r(l,{"must-verify-email":s.mustVerifyEmail,status:s.status,class:"max-w-xl"},null,8,["must-verify-email","status"])])]),e("div",h,[t[2]||(t[2]=e("div",{class:"px-6 py-4 border-b border-gray-700 bg-gradient-to-r from-gray-800 to-gray-700"},[e("div",{class:"flex items-center space-x-3"},[e("div",{class:"p-2 bg-yellow-600 rounded-lg"},[e("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"})])]),e("h3",{class:"text-lg font-semibold text-white"},"Update Password")])],-1)),e("div",v,[r(i,{class:"max-w-xl"})])]),t[4]||(t[4]=e("div",{class:"bg-gradient-to-br from-gray-900 to-gray-800 border border-gray-700 rounded-xl shadow-xl overflow-hidden"},[e("div",{class:"px-6 py-4 border-b border-gray-700 bg-gradient-to-r from-gray-800 to-gray-700"},[e("div",{class:"flex items-center space-x-3"},[e("div",{class:"p-2 bg-blue-600 rounded-lg"},[e("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"}),e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"})])]),e("h3",{class:"text-lg font-semibold text-white"},"Administrator Settings")])]),e("div",{class:"p-6"},[e("div",{class:"space-y-4"},[e("div",{class:"flex items-center justify-between p-4 bg-gray-800 rounded-lg border border-gray-700"},[e("div",null,[e("h4",{class:"text-white font-medium"},"Role"),e("p",{class:"text-gray-400 text-sm"},"System Administrator")]),e("span",{class:"inline-flex items-center px-3 py-1 text-xs font-medium rounded-full bg-red-100 text-red-800 border border-red-200"}," Admin ")]),e("div",{class:"flex items-center justify-between p-4 bg-gray-800 rounded-lg border border-gray-700"},[e("div",null,[e("h4",{class:"text-white font-medium"},"Permissions"),e("p",{class:"text-gray-400 text-sm"},"Full system access")]),e("span",{class:"inline-flex items-center px-3 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800 border border-green-200"}," All Access ")])])])],-1)),e("div",w,[t[3]||(t[3]=e("div",{class:"px-6 py-4 border-b border-red-700 bg-gradient-to-r from-red-800 to-red-700"},[e("div",{class:"flex items-center space-x-3"},[e("div",{class:"p-2 bg-red-600 rounded-lg"},[e("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})])]),e("h3",{class:"text-lg font-semibold text-white"},"Danger Zone")])],-1)),e("div",y,[r(a,{class:"max-w-xl"})])])])])]),_:1})],64))}};export{E as default};
