import{_ as L}from"./AuthenticatedLayout-D449e8ZD.js";import{_ as z}from"./RepairOrderModal-C7iRBsj4.js";import{u as V,r as $,x as P,g as l,i as d,o as n,b as e,t as s,K as E,e as B,F as D,y as R,n as O,d as c,j as S,v as T,k as F,a as k,h as A,w as j,l as H,q as b}from"./app-wnQ52fJE.js";import"./ApplicationLogo-BIrQV_G-.js";import"./DropdownLink-BLptVuux.js";const N={class:"bg-gray-900 border border-gray-700 rounded-xl shadow-2xl w-full max-w-2xl max-h-[90vh] overflow-hidden"},I={class:"px-6 py-4 border-b border-gray-700 bg-gradient-to-r from-red-900 to-red-800"},U={class:"flex items-center justify-between"},q={class:"flex items-center space-x-3"},W={class:"text-red-200 text-sm"},K={class:"p-6 space-y-6 max-h-[60vh] overflow-y-auto"},Q={class:"grid grid-cols-1 md:grid-cols-2 gap-2"},G=["onClick"],J={key:0,class:"mt-1 text-sm text-red-400"},X={class:"bg-gray-800 rounded-lg p-4 border border-gray-700"},Y={class:"flex items-start space-x-3"},Z={class:"px-6 py-4 border-t border-gray-700 bg-gray-800 flex justify-end space-x-3"},_=["disabled"],ee={key:0,class:"animate-spin w-4 h-4",fill:"none",viewBox:"0 0 24 24"},te={key:1,class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},re={__name:"CancelOrderModal",props:{show:{type:Boolean,default:!1},repairOrder:{type:Object,default:null}},emits:["close","cancel"],setup(r,{emit:f}){const v=r,x=f,i=V({cancellation_reason:"",restore_parts:!0}),g=$(!1);P(()=>v.show,y=>{y&&(i.reset(),i.clearErrors())});const m=()=>{if(!i.cancellation_reason.trim()){i.setError("cancellation_reason","Cancellation reason is required.");return}g.value=!0,x("cancel",{reason:i.cancellation_reason,restore_parts:i.restore_parts}),setTimeout(()=>{g.value=!1},1e3)},h=()=>{i.reset(),i.clearErrors(),x("close")},C=["Customer requested cancellation","Parts not available","Customer not responding","Device beyond repair","Customer found alternative solution","Cost exceeds customer budget","Technical issues preventing repair","Other"],M=y=>{i.cancellation_reason=y};return(y,o)=>r.show?(n(),l("div",{key:0,class:"fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm z-50 flex items-center justify-center p-4",onClick:B(h,["self"])},[e("div",N,[e("div",I,[e("div",U,[e("div",q,[o[3]||(o[3]=e("div",{class:"p-2 bg-red-600 rounded-lg"},[e("svg",{class:"w-6 h-6 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"})])],-1)),e("div",null,[o[2]||(o[2]=e("h2",{class:"text-xl font-bold text-white"},"Cancel Repair Order",-1)),e("p",W,"Order #"+s(r.repairOrder?.order_number),1)])]),e("button",{onClick:h,class:"text-red-200 hover:text-white transition-colors duration-200 p-2 hover:bg-red-700 rounded-lg"},o[4]||(o[4]=[e("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))])]),e("div",K,[o[8]||(o[8]=E('<div class="bg-red-900 border border-red-700 rounded-lg p-4"><div class="flex items-start space-x-3"><svg class="w-5 h-5 text-red-400 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"></path></svg><div><h4 class="text-red-200 font-medium">Warning: This action cannot be undone</h4><p class="text-red-300 text-sm mt-1"> Cancelling this repair order will permanently change its status. Make sure this is the correct action to take. </p></div></div></div>',1)),e("form",{onSubmit:B(m,["prevent"]),class:"space-y-6"},[e("div",null,[o[5]||(o[5]=e("label",{class:"block text-sm font-medium text-gray-300 mb-3"}," Common Cancellation Reasons ",-1)),e("div",Q,[(n(),l(D,null,R(C,u=>e("button",{key:u,type:"button",onClick:w=>M(u),class:O(["text-left px-3 py-2 rounded-lg border transition-colors duration-200 text-sm",c(i).cancellation_reason===u?"bg-red-600 border-red-500 text-white":"bg-gray-800 border-gray-600 text-gray-300 hover:bg-gray-700 hover:border-gray-500"])},s(u),11,G)),64))])]),e("div",null,[o[6]||(o[6]=e("label",{for:"cancellation_reason",class:"block text-sm font-medium text-gray-300 mb-2"}," Cancellation Reason * ",-1)),S(e("textarea",{id:"cancellation_reason","onUpdate:modelValue":o[0]||(o[0]=u=>c(i).cancellation_reason=u),rows:"4",class:"w-full rounded-lg bg-gray-800 border-gray-600 text-white shadow-sm focus:border-red-500 focus:ring-red-500 transition-colors duration-200",placeholder:"Please provide a detailed reason for cancelling this repair order...",required:""},null,512),[[T,c(i).cancellation_reason]]),c(i).errors.cancellation_reason?(n(),l("div",J,s(c(i).errors.cancellation_reason),1)):d("",!0)]),e("div",X,[e("div",Y,[S(e("input",{id:"restore_parts","onUpdate:modelValue":o[1]||(o[1]=u=>c(i).restore_parts=u),type:"checkbox",class:"mt-1 rounded border-gray-600 bg-gray-700 text-red-600 focus:ring-red-500 focus:ring-offset-gray-900"},null,512),[[F,c(i).restore_parts]]),o[7]||(o[7]=e("div",{class:"flex-1"},[e("label",{for:"restore_parts",class:"text-sm font-medium text-gray-300 cursor-pointer"}," Restore parts to inventory "),e("p",{class:"text-xs text-gray-400 mt-1"}," If checked, any parts allocated to this order will be returned to available inventory. Uncheck only if parts have been damaged or cannot be reused. ")],-1))])])],32)]),e("div",Z,[e("button",{type:"button",onClick:h,class:"px-6 py-2 border border-gray-600 rounded-lg text-gray-300 hover:bg-gray-700 transition-colors duration-200 font-medium"}," Keep Order "),e("button",{type:"button",onClick:m,disabled:g.value||!c(i).cancellation_reason.trim(),class:O(["px-6 py-2 rounded-lg font-medium transition-all duration-200 flex items-center space-x-2",g.value||!c(i).cancellation_reason.trim()?"bg-gray-600 text-gray-400 cursor-not-allowed":"bg-red-600 hover:bg-red-700 text-white shadow-lg hover:shadow-xl"])},[g.value?(n(),l("svg",ee,o[9]||(o[9]=[e("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"},null,-1),e("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"},null,-1)]))):(n(),l("svg",te,o[10]||(o[10]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"},null,-1)]))),e("span",null,s(g.value?"Cancelling...":"Cancel Order"),1)],10,_)])])])):d("",!0)}},se={class:"flex items-center justify-between"},oe={class:"flex items-center space-x-4"},le={class:"text-2xl font-bold text-white"},ne={class:"p-6 space-y-8"},ie={class:"grid grid-cols-1 md:grid-cols-4 gap-6"},ae={class:"bg-gradient-to-br from-gray-900 to-gray-800 border border-gray-700 rounded-xl p-6 shadow-xl"},de={class:"flex items-center justify-between"},ue={class:"bg-gradient-to-br from-gray-900 to-gray-800 border border-gray-700 rounded-xl p-6 shadow-xl"},ce={class:"flex items-center justify-between"},ge={class:"bg-gradient-to-br from-gray-900 to-gray-800 border border-gray-700 rounded-xl p-6 shadow-xl"},xe={class:"flex items-center justify-between"},me={class:"text-2xl font-bold text-white"},pe={class:"bg-gradient-to-br from-gray-900 to-gray-800 border border-gray-700 rounded-xl p-6 shadow-xl"},be={class:"flex items-center justify-between"},ve={class:"text-lg font-bold text-white"},ye={class:"grid grid-cols-1 lg:grid-cols-3 gap-8"},we={class:"lg:col-span-2"},fe={class:"bg-gradient-to-br from-gray-900 to-gray-800 border border-gray-700 rounded-xl shadow-xl"},he={class:"p-6 space-y-6"},ke={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},Oe={class:"text-white font-medium"},Ce={class:"text-sm text-gray-400"},Me={class:"text-white font-medium"},je={class:"text-sm text-gray-400"},$e={class:"text-white font-medium"},Be={class:"text-sm text-gray-400"},Se={key:0},De={class:"text-white font-medium"},Le={class:"text-sm text-gray-400"},ze={class:"text-white bg-gray-800 rounded-lg p-4"},Ve={key:0},Pe={class:"text-white bg-gray-800 rounded-lg p-4"},Ee={key:1},Re={class:"text-white bg-gray-800 rounded-lg p-4"},Te={key:2},Fe={class:"text-white bg-gray-800 rounded-lg p-4"},Ae={key:3},He={class:"text-white bg-gray-800 rounded-lg p-4"},Ne={class:"lg:col-span-1 space-y-6"},Ie={class:"bg-gradient-to-br from-gray-900 to-gray-800 border border-gray-700 rounded-xl shadow-xl"},Ue={class:"p-6 space-y-3"},qe=["disabled"],We={class:"bg-gradient-to-br from-gray-900 to-gray-800 border border-gray-700 rounded-xl shadow-xl"},Ke={class:"p-6 space-y-4"},Qe={class:"flex justify-between items-center"},Ge={class:"text-white font-medium"},Je={class:"flex justify-between items-center"},Xe={class:"text-white font-medium"},Ye={class:"border-t border-gray-700 pt-4"},Ze={class:"flex justify-between items-center"},_e={class:"text-lg font-bold text-white"},et={class:"bg-gradient-to-br from-gray-900 to-gray-800 border border-gray-700 rounded-xl shadow-xl"},tt={class:"p-6 space-y-4"},rt={class:"flex items-center space-x-3"},st={class:"text-gray-400 text-xs"},ot={key:0,class:"flex items-center space-x-3"},lt={class:"text-gray-400 text-xs"},nt={key:1,class:"flex items-center space-x-3"},it={class:"text-gray-400 text-xs"},at={key:2,class:"flex items-center space-x-3"},dt={class:"text-gray-400 text-xs"},ut={key:0,class:"text-gray-500 text-xs mt-1"},ct={key:3,class:"flex items-center space-x-3"},gt={class:"text-gray-400 text-xs"},yt={__name:"Show",props:{repairOrder:Object,customers:Array,devices:Array,services:Array,technicians:Array},setup(r){const f=r,v=$(!1),x=$(!1),i=()=>{v.value=!0},g=()=>{v.value=!1,b.reload({only:["repairOrder"]})},m=a=>{b.patch(route("repair-orders.update-status",f.repairOrder.id),{status:a},{preserveScroll:!0,onSuccess:()=>{window.toast&&window.toast.success("Order status updated successfully!"),b.reload({only:["repairOrder"]})},onError:t=>{console.error("Status update failed:",t),window.toast&&window.toast.error("Failed to update order status. Please try again.")}})},h=()=>{b.patch(route("repair-orders.mark-delivered",f.repairOrder.id),{},{preserveScroll:!0,onSuccess:()=>{window.toast&&window.toast.success("Order marked as delivered successfully!"),b.reload({only:["repairOrder"]})},onError:a=>{console.error("Mark delivered failed:",a),window.toast&&window.toast.error("Failed to mark order as delivered. Please try again.")}})},C=a=>{b.patch(route("repair-orders.cancel",f.repairOrder.id),{cancellation_reason:a.reason,restore_parts:a.restore_parts},{preserveScroll:!0,onSuccess:()=>{window.toast&&window.toast.success("Order cancelled successfully!"),x.value=!1,b.reload({only:["repairOrder"]})},onError:t=>{console.error("Cancel order failed:",t),window.toast&&window.toast.error("Failed to cancel order. Please try again.")}})},M=a=>{const t={pending:"bg-yellow-100 text-yellow-800 border-yellow-200",in_progress:"bg-blue-100 text-blue-800 border-blue-200",waiting_parts:"bg-orange-100 text-orange-800 border-orange-200",completed:"bg-green-100 text-green-800 border-green-200",cancelled:"bg-red-100 text-red-800 border-red-200",delivered:"bg-purple-100 text-purple-800 border-purple-200"};return t[a]||t.pending},y=a=>{const t={low:"bg-green-100 text-green-800 border-green-200",medium:"bg-yellow-100 text-yellow-800 border-yellow-200",high:"bg-orange-100 text-orange-800 border-orange-200",urgent:"bg-red-100 text-red-800 border-red-200"};return t[a]||t.medium},o=a=>new Date(a).toLocaleDateString(),u=a=>new Date(a).toLocaleString(),w=a=>new Intl.NumberFormat("en-PH",{style:"currency",currency:"PHP",minimumFractionDigits:2}).format(a||0);return(a,t)=>(n(),l(D,null,[k(c(A),{title:`${r.repairOrder.order_number} - Repair Order Details`},null,8,["title"]),k(L,null,{header:j(()=>[e("div",se,[e("div",oe,[k(c(H),{href:a.route("repair-orders.index"),class:"text-gray-400 hover:text-white transition-colors duration-200"},{default:j(()=>t[8]||(t[8]=[e("svg",{class:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 19l-7-7 7-7"})],-1)])),_:1,__:[8]},8,["href"]),e("div",null,[e("h2",le,s(r.repairOrder.order_number),1),t[9]||(t[9]=e("p",{class:"text-gray-400 text-sm"},"Repair Order Details",-1))])]),e("div",{class:"flex items-center space-x-3"},[e("button",{onClick:i,class:"bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center space-x-2"},t[10]||(t[10]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})],-1),e("span",null,"Edit",-1)]))])])]),default:j(()=>[e("div",ne,[e("div",ie,[e("div",ae,[e("div",de,[e("div",null,[t[11]||(t[11]=e("p",{class:"text-sm text-gray-400"},"Status",-1)),e("span",{class:O(["inline-flex items-center px-3 py-1 text-sm font-semibold rounded-full border mt-2",M(r.repairOrder.status)])},s(r.repairOrder.status.replace("_"," ").toUpperCase()),3)]),t[12]||(t[12]=e("div",{class:"p-3 bg-blue-600 rounded-lg"},[e("svg",{class:"w-6 h-6 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1))])]),e("div",ue,[e("div",ce,[e("div",null,[t[13]||(t[13]=e("p",{class:"text-sm text-gray-400"},"Priority",-1)),e("span",{class:O(["inline-flex items-center px-3 py-1 text-sm font-semibold rounded-full border mt-2",y(r.repairOrder.priority)])},s(r.repairOrder.priority.toUpperCase()),3)]),t[14]||(t[14]=e("div",{class:"p-3 bg-orange-600 rounded-lg"},[e("svg",{class:"w-6 h-6 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})])],-1))])]),e("div",ge,[e("div",xe,[e("div",null,[t[15]||(t[15]=e("p",{class:"text-sm text-gray-400"},"Total Cost",-1)),e("p",me,s(w(r.repairOrder.total_cost)),1)]),t[16]||(t[16]=e("div",{class:"p-3 bg-green-600 rounded-lg"},[e("svg",{class:"w-6 h-6 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})])],-1))])]),e("div",pe,[e("div",be,[e("div",null,[t[17]||(t[17]=e("p",{class:"text-sm text-gray-400"},"Created",-1)),e("p",ve,s(o(r.repairOrder.created_at)),1)]),t[18]||(t[18]=e("div",{class:"p-3 bg-purple-600 rounded-lg"},[e("svg",{class:"w-6 h-6 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})])],-1))])])]),e("div",ye,[e("div",we,[e("div",fe,[t[28]||(t[28]=e("div",{class:"px-6 py-4 border-b border-gray-700 bg-gradient-to-r from-gray-800 to-gray-700 rounded-t-xl"},[e("h3",{class:"text-lg font-semibold text-white"},"Order Information")],-1)),e("div",he,[e("div",ke,[e("div",null,[t[19]||(t[19]=e("label",{class:"text-sm font-medium text-gray-400"},"Customer",-1)),e("p",Oe,s(r.repairOrder.customer?.full_name),1),e("p",Ce,s(r.repairOrder.customer?.phone),1)]),e("div",null,[t[20]||(t[20]=e("label",{class:"text-sm font-medium text-gray-400"},"Device",-1)),e("p",Me,s(r.repairOrder.device?.brand)+" "+s(r.repairOrder.device?.model),1),e("p",je,s(r.repairOrder.device?.device_type?.name),1)]),e("div",null,[t[21]||(t[21]=e("label",{class:"text-sm font-medium text-gray-400"},"Service",-1)),e("p",$e,s(r.repairOrder.service?.name),1),e("p",Be,s(w(r.repairOrder.service?.base_price)),1)]),r.repairOrder.technician?(n(),l("div",Se,[t[22]||(t[22]=e("label",{class:"text-sm font-medium text-gray-400"},"Assigned Technician",-1)),e("p",De,s(r.repairOrder.technician?.user?.name),1),e("p",Le,s(r.repairOrder.technician?.specialization),1)])):d("",!0)]),e("div",null,[t[23]||(t[23]=e("label",{class:"text-sm font-medium text-gray-400 mb-2 block"},"Issue Description",-1)),e("p",ze,s(r.repairOrder.issue_description),1)]),r.repairOrder.customer_notes?(n(),l("div",Ve,[t[24]||(t[24]=e("label",{class:"text-sm font-medium text-gray-400 mb-2 block"},"Customer Notes",-1)),e("p",Pe,s(r.repairOrder.customer_notes),1)])):d("",!0),r.repairOrder.diagnosis?(n(),l("div",Ee,[t[25]||(t[25]=e("label",{class:"text-sm font-medium text-gray-400 mb-2 block"},"Diagnosis",-1)),e("p",Re,s(r.repairOrder.diagnosis),1)])):d("",!0),r.repairOrder.solution?(n(),l("div",Te,[t[26]||(t[26]=e("label",{class:"text-sm font-medium text-gray-400 mb-2 block"},"Solution",-1)),e("p",Fe,s(r.repairOrder.solution),1)])):d("",!0),r.repairOrder.internal_notes?(n(),l("div",Ae,[t[27]||(t[27]=e("label",{class:"text-sm font-medium text-gray-400 mb-2 block"},"Internal Notes",-1)),e("p",He,s(r.repairOrder.internal_notes),1)])):d("",!0)])])]),e("div",Ne,[e("div",Ie,[t[35]||(t[35]=e("div",{class:"px-6 py-4 border-b border-gray-700 bg-gradient-to-r from-gray-800 to-gray-700 rounded-t-xl"},[e("h3",{class:"text-lg font-semibold text-white"},"Quick Actions")],-1)),e("div",Ue,[r.repairOrder.status==="pending"?(n(),l("button",{key:0,onClick:t[0]||(t[0]=p=>m("in_progress")),class:"w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center space-x-2"},t[29]||(t[29]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h8m2-10V7a2 2 0 01-2 2H5a2 2 0 01-2-2V4a2 2 0 012-2h14a2 2 0 012 2z"})],-1),e("span",null,"Start Work",-1)]))):d("",!0),r.repairOrder.status==="in_progress"?(n(),l("button",{key:1,onClick:t[1]||(t[1]=p=>m("waiting_parts")),class:"w-full bg-orange-600 hover:bg-orange-700 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center space-x-2"},t[30]||(t[30]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})],-1),e("span",null,"Wait for Parts",-1)]))):d("",!0),r.repairOrder.status==="in_progress"?(n(),l("button",{key:2,onClick:t[2]||(t[2]=p=>m("completed")),class:"w-full bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center space-x-2"},t[31]||(t[31]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})],-1),e("span",null,"Mark Complete",-1)]))):d("",!0),r.repairOrder.status==="waiting_parts"?(n(),l("button",{key:3,onClick:t[3]||(t[3]=p=>m("in_progress")),class:"w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center space-x-2"},t[32]||(t[32]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 10V3L4 14h7v7l9-11h-7z"})],-1),e("span",null,"Resume Work",-1)]))):d("",!0),r.repairOrder.status==="completed"||r.repairOrder.status==="delivered"?(n(),l("button",{key:4,onClick:t[4]||(t[4]=p=>r.repairOrder.status==="completed"?h():null),disabled:r.repairOrder.status==="delivered",class:O(["w-full px-4 py-2 rounded-lg transition-colors duration-200 flex items-center space-x-2",r.repairOrder.status==="delivered"?"bg-gray-600 text-gray-400 cursor-not-allowed":"bg-purple-600 hover:bg-purple-700 text-white"])},[t[33]||(t[33]=e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"})],-1)),e("span",null,s(r.repairOrder.status==="delivered"?"Delivered":"Mark Delivered"),1)],10,qe)):d("",!0),["pending","in_progress","waiting_parts"].includes(r.repairOrder.status)?(n(),l("button",{key:5,onClick:t[5]||(t[5]=p=>x.value=!0),class:"w-full bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center space-x-2"},t[34]||(t[34]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1),e("span",null,"Cancel Order",-1)]))):d("",!0)])]),e("div",We,[t[39]||(t[39]=e("div",{class:"px-6 py-4 border-b border-gray-700 bg-gradient-to-r from-gray-800 to-gray-700 rounded-t-xl"},[e("h3",{class:"text-lg font-semibold text-white"},"Cost Breakdown")],-1)),e("div",Ke,[e("div",Qe,[t[36]||(t[36]=e("span",{class:"text-gray-400"},"Labor Cost",-1)),e("span",Ge,s(w(r.repairOrder.labor_cost)),1)]),e("div",Je,[t[37]||(t[37]=e("span",{class:"text-gray-400"},"Parts Cost",-1)),e("span",Xe,s(w(r.repairOrder.parts_cost)),1)]),e("div",Ye,[e("div",Ze,[t[38]||(t[38]=e("span",{class:"text-lg font-semibold text-white"},"Total",-1)),e("span",_e,s(w(r.repairOrder.total_cost)),1)])])])]),e("div",et,[t[50]||(t[50]=e("div",{class:"px-6 py-4 border-b border-gray-700 bg-gradient-to-r from-gray-800 to-gray-700 rounded-t-xl"},[e("h3",{class:"text-lg font-semibold text-white"},"Timeline")],-1)),e("div",tt,[e("div",rt,[t[41]||(t[41]=e("div",{class:"w-3 h-3 bg-blue-500 rounded-full"},null,-1)),e("div",null,[t[40]||(t[40]=e("p",{class:"text-white text-sm"},"Order Created",-1)),e("p",st,s(u(r.repairOrder.created_at)),1)])]),r.repairOrder.estimated_completion?(n(),l("div",ot,[t[43]||(t[43]=e("div",{class:"w-3 h-3 bg-yellow-500 rounded-full"},null,-1)),e("div",null,[t[42]||(t[42]=e("p",{class:"text-white text-sm"},"Estimated Completion",-1)),e("p",lt,s(u(r.repairOrder.estimated_completion)),1)])])):d("",!0),r.repairOrder.actual_completion?(n(),l("div",nt,[t[45]||(t[45]=e("div",{class:"w-3 h-3 bg-green-500 rounded-full"},null,-1)),e("div",null,[t[44]||(t[44]=e("p",{class:"text-white text-sm"},"Completed",-1)),e("p",it,s(u(r.repairOrder.actual_completion)),1)])])):d("",!0),r.repairOrder.cancelled_at?(n(),l("div",at,[t[47]||(t[47]=e("div",{class:"w-3 h-3 bg-red-500 rounded-full"},null,-1)),e("div",null,[t[46]||(t[46]=e("p",{class:"text-white text-sm"},"Cancelled",-1)),e("p",dt,s(u(r.repairOrder.cancelled_at)),1),r.repairOrder.cancellation_reason?(n(),l("p",ut," Reason: "+s(r.repairOrder.cancellation_reason),1)):d("",!0)])])):d("",!0),r.repairOrder.delivered_at?(n(),l("div",ct,[t[49]||(t[49]=e("div",{class:"w-3 h-3 bg-purple-500 rounded-full"},null,-1)),e("div",null,[t[48]||(t[48]=e("p",{class:"text-white text-sm"},"Delivered",-1)),e("p",gt,s(u(r.repairOrder.delivered_at)),1)])])):d("",!0)])])])])]),k(z,{show:v.value,"repair-order":r.repairOrder,customers:r.customers,devices:r.devices,services:r.services,technicians:r.technicians,onClose:t[6]||(t[6]=p=>v.value=!1),onSaved:g},null,8,["show","repair-order","customers","devices","services","technicians"]),k(re,{show:x.value,"repair-order":r.repairOrder,onClose:t[7]||(t[7]=p=>x.value=!1),onCancel:C},null,8,["show","repair-order"])]),_:1})],64))}};export{yt as default};
