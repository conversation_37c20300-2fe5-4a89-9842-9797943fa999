import{_ as t,g as o,o as r,s as n}from"./app-wnQ52fJE.js";const s={},a={class:"inline-flex items-center rounded-md border border-transparent bg-gray-800 px-4 py-2 text-xs font-semibold uppercase tracking-widest text-white transition duration-150 ease-in-out hover:bg-gray-700 focus:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 active:bg-gray-900"};function i(e,c){return r(),o("button",a,[n(e.$slots,"default")])}const u=t(s,[["render",i]]);export{u as P};
